/* App组件样式 */
.app-layout {
  height: 100vh;
  overflow: hidden;
}

.main-layout {
  height: 100vh;
  overflow: hidden;
}

.main-content {
  height: calc(100vh - 64px);
  overflow-y: auto;
  background: #f0f2f5;
  padding: 0;
}

/* 页面容器 */
.page-container {
  padding: 24px;
  min-height: 100%;
  background: #f0f2f5;
}

/* 内容区域 */
.content-area {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 加载状态 */
.app-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f0f2f5;
}

/* 错误状态 */
.app-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f0f2f5;
  padding: 40px;
  text-align: center;
}

.app-error .error-icon {
  font-size: 64px;
  color: #ff4d4f;
  margin-bottom: 24px;
}

.app-error .error-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
}

.app-error .error-description {
  font-size: 16px;
  color: #8c8c8c;
  margin-bottom: 32px;
  max-width: 500px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    height: calc(100vh - 56px);
  }
  
  .page-container {
    padding: 16px;
  }
  
  .content-area {
    padding: 16px;
    border-radius: 6px;
  }
  
  .app-error {
    padding: 24px;
  }
  
  .app-error .error-icon {
    font-size: 48px;
  }
  
  .app-error .error-title {
    font-size: 20px;
  }
  
  .app-error .error-description {
    font-size: 14px;
  }
}

/* 路由过渡动画 */
.route-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.route-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

.route-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.route-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

/* 滚动条样式 */
.main-content::-webkit-scrollbar {
  width: 8px;
}

.main-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 通用工具类 */
.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.margin-bottom-24 {
  margin-bottom: 24px;
}

.margin-top-24 {
  margin-top: 24px;
}

.padding-24 {
  padding: 24px;
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
