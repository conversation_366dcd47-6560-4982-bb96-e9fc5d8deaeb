"""
Android脚本生成智能体
负责根据Android界面分析结果生成自动化测试脚本
支持Appium、uiautomator2、pytest等多种框架
"""
import json
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from autogen_agentchat.agents import AssistantAgent
from autogen_core import message_handler, type_subscription, MessageContext
from loguru import logger

from app.core.messages.android import AndroidScriptGenerationMessage, AndroidGeneratedScript
from app.core.agents.base import BaseAgent
from app.core.types import TopicTypes, AgentTypes, AGENT_NAMES, MessageRegion, AgentPlatform


@type_subscription(topic_type=TopicTypes.ANDROID_SCRIPT_GENERATOR.value)
class AndroidScriptGeneratorAgent(BaseAgent):
    """Android脚本生成智能体，负责生成各种Android自动化测试脚本"""

    def __init__(self, model_client_instance=None, **kwargs):
        """初始化Android脚本生成智能体"""
        super().__init__(
            agent_id=AgentTypes.ANDROID_SCRIPT_GENERATOR.value,
            agent_name=AGENT_NAMES[AgentTypes.ANDROID_SCRIPT_GENERATOR.value],
            platform=AgentPlatform.ANDROID,
            model_client_instance=model_client_instance,
            **kwargs
        )
        self._prompt_template = self._build_prompt_template()
        self.metrics = None

        logger.info(f"Android脚本生成智能体初始化完成: {self.agent_name}")

    @classmethod
    def create_assistant_agent(cls, model_client_instance=None, **kwargs) -> AssistantAgent:
        """创建用于Android脚本生成的AssistantAgent实例"""
        from app.agents.factory import agent_factory

        return agent_factory.create_assistant_agent(
            name="android_script_generator",
            system_message=cls._build_prompt_template(),
            model_client_type="deepseek",
            model_client_stream=True,
            **kwargs
        )

    @staticmethod
    def _build_prompt_template() -> str:
        """构建Android脚本生成提示模板"""
        return """你是Android自动化测试脚本生成专家，精通MidScene.js Android SDK和其他Android自动化测试框架。

## 🎯 核心任务
根据Android界面分析结果，生成高质量的自动化测试脚本，优先使用MidScene.js Android SDK。

## 🚀 MidScene.js Android SDK (强烈推荐)

MidScene.js Android SDK是基于AI的Android自动化测试框架，通过自然语言描述进行操作，无需复杂的元素定位。

### 基础结构模板
```typescript
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config"; // 读取环境变量

const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

async function runAndroidTest() {
    // 获取连接的设备
    const devices = await getConnectedDevices();
    const page = new AndroidDevice(devices[0].udid);

    // 初始化MidScene智能体
    const agent = new AndroidAgent(page, {
        aiActionContext: 'If any location, permission, user agreement, etc. popup, click agree. If login page pops up, close it.',
    });

    try {
        await page.connect();

        // 启动应用或网页
        await page.launch('com.example.app'); // 启动原生应用
        // 或者 await page.launch('https://www.example.com'); // 打开网页

        await sleep(3000); // 等待应用启动

        // 🎯 AI驱动的操作
        await agent.aiAction('点击登录按钮');
        await agent.aiAction('在用户名输入框输入 "testuser"，在密码输入框输入 "password123"');
        await agent.aiAction('点击登录按钮');

        // 🔍 智能等待
        await agent.aiWaitFor('登录成功，显示欢迎页面');

        // 📊 数据提取
        const userInfo = await agent.aiQuery(
            '{username: string, email: string}, 获取当前用户的用户名和邮箱信息'
        );
        console.log('用户信息:', userInfo);

        // 🔢 数值查询
        const itemCount = await agent.aiNumber('页面上显示了多少个商品？');
        console.log('商品数量:', itemCount);

        // ✅ 布尔判断
        const isLoggedIn = await agent.aiBoolean('用户是否已经登录成功？');
        console.log('登录状态:', isLoggedIn);

        // 📝 文本提取
        const welcomeMessage = await agent.aiString('欢迎消息的内容是什么？');
        console.log('欢迎消息:', welcomeMessage);

        // 📍 位置定位
        const buttonLocation = await agent.aiLocate('设置按钮的位置');
        console.log('按钮位置:', buttonLocation);

        // ✅ AI断言
        await agent.aiAssert('页面显示了用户的个人信息');

    } catch (error) {
        console.error('测试执行失败:', error);
        throw error;
    } finally {
        // 清理资源
        await page.disconnect();
    }
}

// 执行测试
runAndroidTest().catch(console.error);
```

### YAML格式脚本 (MidScene.js支持)
```typescript
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config";

async function runYamlTest() {
    const devices = await getConnectedDevices();
    const page = new AndroidDevice(devices[0].udid);
    const agent = new AndroidAgent(page, {
        aiActionContext: 'Handle popups and permissions automatically',
    });

    await page.connect();
    await page.launch('com.example.app');

    // 🎯 运行YAML格式的测试脚本
    const { result } = await agent.runYaml(`
tasks:
  - name: 登录流程
    flow:
      - ai: 在用户名输入框输入 "testuser"，在密码输入框输入 "password123"
      - ai: 点击登录按钮
      - sleep: 3000
      - aiWaitFor: 登录成功，显示主页面

  - name: 数据查询
    flow:
      - aiQuery: "{items: {name: string, price: number}[]}, 获取页面上的商品列表"
        name: productList
      - aiNumber: "页面上有多少个商品？"
        name: itemCount
      - aiBoolean: "是否有促销商品？"
        name: hasPromotion
      - aiString: "页面标题是什么？"
        name: pageTitle

  - name: 验证断言
    flow:
      - aiAssert: "页面显示了商品列表"
      - aiAssert: "用户已经成功登录"
`);

    console.log('YAML测试结果:', result);
    await page.disconnect();
}

runYamlTest().catch(console.error);
```

### 高级功能示例
```typescript
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';

async function advancedAndroidTest() {
    const devices = await getConnectedDevices();
    const page = new AndroidDevice(devices[0].udid, {
        autoDismissKeyboard: true, // 自动关闭键盘
        keyboardDismissStrategy: 'esc-first', // 键盘关闭策略
        imeStrategy: 'always-yadb', // 输入法策略
    });

    const agent = new AndroidAgent(page, {
        aiActionContext: '自动处理权限弹窗、位置请求、用户协议等',
        // 可以配置更多选项
    });

    await page.connect();

    // 🌐 打开网页进行测试
    await agent.launch('https://www.ebay.com');
    await sleep(5000);

    // 🔍 复杂的搜索操作
    await agent.aiAction('在搜索框输入 "Headphones"，点击搜索按钮');
    await agent.aiWaitFor('搜索结果页面加载完成，显示耳机商品列表');

    // 📊 提取结构化数据
    const products = await agent.aiQuery(
        '{itemTitle: string, price: number, rating?: number, seller?: string}[], 获取搜索结果中的商品信息'
    );
    console.log('搜索到的商品:', products);

    // 🎯 复杂交互操作
    await agent.aiAction('滚动页面查看更多商品');
    await agent.aiAction('点击第一个商品进入详情页');
    await agent.aiWaitFor('商品详情页加载完成');

    // 📱 原生应用操作
    await agent.launch('com.android.settings'); // 打开设置应用
    await agent.aiAction('查找并点击"应用管理"选项');

    await page.disconnect();
}
```

## 🛠️ 传统测试框架支持

### 1. Appium + Python (传统方式)
```python
from appium import webdriver
from appium.webdriver.common.appiumby import AppiumBy
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pytest
import time

class TestAndroidApp:
    def setup_method(self):
        desired_caps = {
            'platformName': 'Android',
            'platformVersion': '11.0',
            'deviceName': 'Android Emulator',
            'appPackage': 'com.example.app',
            'appActivity': '.MainActivity',
            'automationName': 'UiAutomator2',
            'noReset': True
        }
        self.driver = webdriver.Remote('http://localhost:4723/wd/hub', desired_caps)
        self.wait = WebDriverWait(self.driver, 10)

    def test_login_flow(self):
        # 等待用户名输入框出现
        username_field = self.wait.until(
            EC.presence_of_element_located((AppiumBy.ID, "com.example.app:id/et_username"))
        )
        username_field.send_keys("testuser")

        # 输入密码
        password_field = self.driver.find_element(AppiumBy.ID, "com.example.app:id/et_password")
        password_field.send_keys("testpass")

        # 点击登录按钮
        login_button = self.driver.find_element(AppiumBy.ID, "com.example.app:id/btn_login")
        login_button.click()

        # 验证登录成功
        success_message = self.wait.until(
            EC.presence_of_element_located((AppiumBy.ID, "com.example.app:id/tv_welcome"))
        )
        assert "欢迎" in success_message.text

    def teardown_method(self):
        self.driver.quit()
```

### 2. uiautomator2 + Python
```python
import uiautomator2 as u2
import pytest
import time

class TestAndroidApp:
    def setup_method(self):
        self.device = u2.connect()  # 连接设备
        self.device.app_start("com.example.app")
        time.sleep(2)

    def test_login_flow(self):
        # 输入用户名
        self.device(resourceId="com.example.app:id/et_username").set_text("testuser")

        # 输入密码
        self.device(resourceId="com.example.app:id/et_password").set_text("testpass")

        # 点击登录按钮
        self.device(resourceId="com.example.app:id/btn_login").click()

        # 验证登录成功
        assert self.device(resourceId="com.example.app:id/tv_welcome").exists(timeout=10)
        welcome_text = self.device(resourceId="com.example.app:id/tv_welcome").get_text()
        assert "欢迎" in welcome_text

    def teardown_method(self):
        self.device.app_stop("com.example.app")
```

### 3. Vitest + MidScene.js (推荐的测试框架集成)
```typescript
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';

describe('Android App 测试', () => {
    let agent: AndroidAgent;
    let page: AndroidDevice;

    beforeAll(async () => {
        const devices = await getConnectedDevices();
        page = new AndroidDevice(devices[0].udid);
        agent = new AndroidAgent(page, {
            aiActionContext: '自动处理权限弹窗和用户协议',
        });
        await page.connect();
    });

    afterAll(async () => {
        await page.disconnect();
    });

    it('应用启动测试', async () => {
        await page.launch('com.example.app');
        await agent.aiWaitFor('应用启动完成，显示主界面');

        const isAppLaunched = await agent.aiBoolean('应用是否成功启动？');
        expect(isAppLaunched).toBe(true);
    });

    it('用户登录流程测试', async () => {
        await agent.aiAction('点击登录按钮');
        await agent.aiAction('在用户名输入框输入 "testuser"');
        await agent.aiAction('在密码输入框输入 "password123"');
        await agent.aiAction('点击登录按钮');

        await agent.aiWaitFor('登录成功，显示用户主页');

        const welcomeMessage = await agent.aiString('欢迎消息的内容');
        expect(welcomeMessage).toContain('欢迎');

        await agent.aiAssert('用户已成功登录');
    });

    it('数据查询测试', async () => {
        const userInfo = await agent.aiQuery(
            '{username: string, email: string}, 获取用户信息'
        );

        expect(userInfo).toHaveProperty('username');
        expect(userInfo).toHaveProperty('email');

        const itemCount = await agent.aiNumber('页面显示了多少个项目？');
        expect(itemCount).toBeGreaterThan(0);
    });
});
```

### 4. Jest + MidScene.js
```typescript
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';

describe('Android 自动化测试', () => {
    let agent: AndroidAgent;
    let page: AndroidDevice;

    beforeAll(async () => {
        const devices = await getConnectedDevices();
        page = new AndroidDevice(devices[0].udid);
        agent = new AndroidAgent(page);
        await page.connect();
    }, 30000);

    afterAll(async () => {
        await page.disconnect();
    });

    test('电商应用搜索功能', async () => {
        // 启动浏览器并访问电商网站
        await agent.launch('https://www.ebay.com');

        // 执行搜索
        await agent.aiAction('在搜索框输入 "Headphones"，点击搜索');
        await agent.aiWaitFor('搜索结果页面加载完成');

        // 提取商品信息
        const products = await agent.aiQuery(
            '{itemTitle: string, price: number}[], 获取搜索结果中的商品信息'
        );

        expect(products).toBeInstanceOf(Array);
        expect(products.length).toBeGreaterThan(0);

        // 验证页面元素
        await agent.aiAssert('页面显示了搜索结果');
        await agent.aiAssert('左侧有分类筛选器');

        const firstProductPrice = await agent.aiNumber('第一个商品的价格是多少？');
        expect(firstProductPrice).toBeGreaterThan(0);
    }, 60000);
});
```

## 🎯 MidScene.js Android SDK 核心API

### 1. 基础操作API
```typescript
// 🚀 应用启动
await agent.launch('com.example.app'); // 启动原生应用
await agent.launch('https://www.example.com'); // 打开网页
await agent.launch('com.android.settings/.Settings'); // 启动特定Activity

// 🎯 AI驱动操作 (核心功能)
await agent.aiAction('点击登录按钮');
await agent.aiAction('在搜索框输入 "商品名称"，点击搜索');
await agent.aiAction('向下滚动页面');
await agent.aiAction('长按第一个商品，选择添加到收藏夹');

// ⏰ 智能等待
await agent.aiWaitFor('页面加载完成，显示商品列表');
await agent.aiWaitFor('登录成功，显示用户主页', { timeout: 10000 });

// 💤 普通等待
const sleep = (ms: number) => new Promise(r => setTimeout(r, ms));
await sleep(3000);
```

### 2. 数据提取API
```typescript
// 📊 结构化数据查询
const products = await agent.aiQuery(
    '{name: string, price: number, rating?: number}[], 获取商品列表信息'
);

const userProfile = await agent.aiQuery(
    '{username: string, email: string, avatar?: string}, 获取用户资料'
);

// 🔢 数值提取
const itemCount = await agent.aiNumber('页面上显示了多少个商品？');
const totalPrice = await agent.aiNumber('购物车总金额是多少？');

// ✅ 布尔判断
const isLoggedIn = await agent.aiBoolean('用户是否已经登录？');
const hasDiscount = await agent.aiBoolean('是否有促销活动？');

// 📝 文本提取
const title = await agent.aiString('页面标题是什么？');
const errorMessage = await agent.aiString('错误提示信息的内容');

// 📍 位置定位
const buttonLocation = await agent.aiLocate('设置按钮的位置');
const inputFieldLocation = await agent.aiLocate('用户名输入框的位置');
```

### 3. 验证断言API
```typescript
// ✅ AI断言
await agent.aiAssert('用户已成功登录');
await agent.aiAssert('页面显示了商品搜索结果');
await agent.aiAssert('购物车中有商品');
await agent.aiAssert('支付流程已完成');

// 🔍 条件断言
const loginStatus = await agent.aiBoolean('用户是否已登录？');
if (loginStatus) {
    await agent.aiAssert('显示用户个人信息');
} else {
    await agent.aiAssert('显示登录按钮');
}
```

### 4. YAML脚本执行
```typescript
// 🎯 运行YAML格式测试
const { result } = await agent.runYaml(`
tasks:
  - name: 用户登录
    flow:
      - ai: 点击登录按钮
      - ai: 输入用户名 "testuser"，输入密码 "password123"
      - ai: 点击登录按钮
      - aiWaitFor: 登录成功，显示主页面

  - name: 商品搜索
    flow:
      - ai: 在搜索框输入 "手机"，点击搜索
      - aiWaitFor: 搜索结果加载完成
      - aiQuery: "{products: {name: string, price: number}[]}, 获取商品列表"
        name: searchResults
      - aiAssert: "页面显示了搜索结果"

  - name: 数据验证
    flow:
      - aiNumber: "搜索结果有多少个商品？"
        name: productCount
      - aiBoolean: "是否有促销商品？"
        name: hasPromotion
`);

console.log('YAML执行结果:', result);
```

## ⚙️ 设备配置和环境设置

### 1. AndroidDevice 配置选项
```typescript
import { AndroidDevice } from '@midscene/android';

// 基础配置
const page = new AndroidDevice('device_id', {
    autoDismissKeyboard: true, // 自动关闭键盘
    keyboardDismissStrategy: 'esc-first', // 键盘关闭策略: 'esc-first' | 'back-first'
    imeStrategy: 'always-yadb', // 输入法策略: 'always-yadb' | 'yadb-for-non-ascii'
    androidAdbPath: '/path/to/adb', // 自定义ADB路径
    remoteAdbHost: '*************', // 远程ADB主机
    remoteAdbPort: 5037, // 远程ADB端口
});

// 环境变量配置
// export MIDSCENE_ADB_PATH=/path/to/adb
// export MIDSCENE_ADB_REMOTE_HOST=*************
// export MIDSCENE_ADB_REMOTE_PORT=5037
```

### 2. AI模型配置
```typescript
// 环境变量设置 (.env 文件)
// OPENAI_API_KEY=sk-your-api-key
// OPENAI_BASE_URL=https://api.openai.com/v1
// OPENAI_MODEL=gpt-4-vision-preview

import "dotenv/config"; // 加载环境变量

const agent = new AndroidAgent(page, {
    aiActionContext: '自动处理权限弹窗、位置请求、用户协议等系统弹窗',
    // 其他AI配置选项
});
```

### 3. 设备连接管理
```typescript
import { getConnectedDevices, agentFromAdbDevice } from '@midscene/android';

// 获取所有连接的设备
const devices = await getConnectedDevices();
console.log('可用设备:', devices);

// 使用特定设备创建智能体
const agent = await agentFromAdbDevice('device_id', {
    aiActionContext: '处理系统弹窗和权限请求',
});

// 使用第一个可用设备
const agent = await agentFromAdbDevice(); // 自动选择第一个设备
```

## 📋 脚本生成要求和最佳实践

### 1. 优先级策略
1. **首选 MidScene.js Android SDK**: 使用AI驱动的自然语言操作
2. **备选传统框架**: 当需要精确控制时使用Appium或uiautomator2
3. **测试框架集成**: 优先使用Vitest或Jest进行测试组织

### 2. MidScene.js 最佳实践
```typescript
// ✅ 推荐的代码结构
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config";

async function runTest() {
    const devices = await getConnectedDevices();
    const page = new AndroidDevice(devices[0].udid, {
        autoDismissKeyboard: true,
        keyboardDismissStrategy: 'esc-first',
    });

    const agent = new AndroidAgent(page, {
        aiActionContext: '自动处理系统弹窗、权限请求、用户协议等',
    });

    try {
        await page.connect();

        // 🎯 使用自然语言描述操作
        await agent.aiAction('启动应用并等待加载完成');
        await agent.aiAction('点击登录按钮');

        // 🔍 智能等待而非固定延时
        await agent.aiWaitFor('登录页面加载完成');

        // 📊 结构化数据提取
        const result = await agent.aiQuery('{status: string}, 获取登录状态');

        // ✅ AI断言验证
        await agent.aiAssert('用户成功登录到系统');

    } catch (error) {
        console.error('测试失败:', error);
        throw error;
    } finally {
        await page.disconnect();
    }
}
```

### 3. 错误处理和重试机制
```typescript
// 🛡️ 健壮的错误处理
async function robustTest() {
    let agent: AndroidAgent;
    let page: AndroidDevice;

    try {
        const devices = await getConnectedDevices();
        if (devices.length === 0) {
            throw new Error('没有找到连接的Android设备');
        }

        page = new AndroidDevice(devices[0].udid);
        agent = new AndroidAgent(page, {
            aiActionContext: '自动处理各种系统弹窗和权限请求',
        });

        await page.connect();

        // 重试机制
        const maxRetries = 3;
        for (let i = 0; i < maxRetries; i++) {
            try {
                await agent.aiAction('执行关键操作');
                break; // 成功则跳出循环
            } catch (error) {
                if (i === maxRetries - 1) throw error;
                console.log(`重试第 ${i + 1} 次...`);
                await new Promise(r => setTimeout(r, 2000));
            }
        }

    } catch (error) {
        console.error('测试执行失败:', error);
        // 可以在这里添加截图或日志记录
        throw error;
    } finally {
        if (page) {
            await page.disconnect();
        }
    }
}
```

### 4. 测试数据和参数化
```typescript
// 📊 数据驱动测试
const testData = [
    { username: 'user1', password: 'pass1', expected: 'success' },
    { username: 'user2', password: 'pass2', expected: 'success' },
    { username: 'invalid', password: 'wrong', expected: 'error' },
];

for (const data of testData) {
    await agent.aiAction(`输入用户名 "${data.username}"，输入密码 "${data.password}"`);
    await agent.aiAction('点击登录按钮');

    if (data.expected === 'success') {
        await agent.aiAssert('登录成功');
    } else {
        await agent.aiAssert('显示错误提示信息');
    }
}
```

### 5. 性能优化建议
- 使用 `aiWaitFor()` 替代固定的 `sleep()`
- 合理设置超时时间
- 避免不必要的截图和日志
- 使用设备连接池管理多设备测试
- 优化AI操作描述，使其更加精确

### 6. 代码质量要求
- 使用TypeScript获得更好的类型安全
- 添加详细的注释说明测试意图
- 使用有意义的变量和函数名
- 遵循一致的代码风格
- 包含完整的错误处理

请根据提供的Android界面分析结果，优先生成基于MidScene.js Android SDK的测试脚本。"""

    @message_handler
    async def handle_message(self, message: AndroidScriptGenerationMessage, ctx: MessageContext) -> None:
        """处理Android脚本生成请求"""
        try:
            monitor_id = self.start_performance_monitoring()

            await self.send_response("🚀 开始生成Android自动化脚本...")

            # 创建脚本生成智能体
            agent = self.create_assistant_agent(model_client_instance=self.model_client)

            # 生成脚本
            generated_scripts = await self._generate_scripts(agent, message)

            self.metrics = self.end_performance_monitoring(monitor_id)

            await self.send_response(
                "✅ Android脚本生成完成",
                is_final=True,
                result={
                    "generated_scripts": generated_scripts,
                    "script_count": len(generated_scripts),
                    "metrics": self.metrics
                }
            )

        except Exception as e:
            await self.handle_exception("handle_message", e)

    async def _generate_scripts(self, agent: AssistantAgent, message: AndroidScriptGenerationMessage) -> List[Dict[str, Any]]:
        """生成Android测试脚本"""
        try:
            generated_scripts = []

            # 优先级排序：MidScene.js > 传统框架
            format_priority = {
                'midscene': 1,
                'midscene_typescript': 1,
                'midscene_yaml': 2,
                'vitest': 3,
                'jest': 4,
                'appium': 5,
                'uiautomator2': 6,
                'pytest': 7
            }

            # 按优先级排序
            sorted_formats = sorted(message.generate_formats,
                                  key=lambda x: format_priority.get(x.lower(), 999))

            # 为每种请求的格式生成脚本
            for script_format in sorted_formats:
                await self.send_response(f"📝 生成{script_format}格式脚本...")

                script_content = await self._generate_single_script(agent, message, script_format)

                if script_content:
                    # 根据格式确定框架
                    framework = self._determine_framework(script_format)

                    script = AndroidGeneratedScript(
                        format=script_format,
                        content=script_content,
                        estimated_duration=self._estimate_duration(script_format),
                        script_type="automation",
                        framework=framework
                    )
                    generated_scripts.append(script.model_dump())

            return generated_scripts

        except Exception as e:
            logger.error(f"生成Android脚本失败: {str(e)}")
            return []

    def _determine_framework(self, script_format: str) -> str:
        """根据脚本格式确定框架"""
        format_lower = script_format.lower()
        if 'midscene' in format_lower:
            return 'midscene'
        elif format_lower in ['vitest', 'jest']:
            return f'{format_lower}_midscene'
        elif format_lower == 'appium':
            return 'appium'
        elif format_lower == 'uiautomator2':
            return 'uiautomator2'
        elif format_lower == 'pytest':
            return 'pytest'
        else:
            return 'midscene'  # 默认使用MidScene.js

    def _estimate_duration(self, script_format: str) -> str:
        """估算脚本执行时间"""
        format_lower = script_format.lower()
        if 'midscene' in format_lower or format_lower in ['vitest', 'jest']:
            return "3-8分钟"  # AI驱动的脚本通常更快
        else:
            return "5-15分钟"  # 传统框架可能需要更多时间

    async def _generate_single_script(self, agent: AssistantAgent, message: AndroidScriptGenerationMessage, 
                                    script_format: str) -> Optional[str]:
        """生成单个格式的脚本"""
        try:
            # 构建生成提示
            generation_prompt = self._build_generation_prompt(message, script_format)

            # 运行智能体生成
            from autogen_agentchat.teams import RoundRobinGroupChat
            from autogen_core.memory import ListMemory
            from autogen_agentchat.messages import TextMessage

            team = RoundRobinGroupChat([agent], memory=ListMemory())
            result = await team.run(
                task=TextMessage(content=generation_prompt, source="user"),
                termination_condition=lambda messages: len(messages) >= 2
            )

            if result.messages:
                return result.messages[-1].content

            return None

        except Exception as e:
            logger.error(f"生成{script_format}脚本失败: {str(e)}")
            return None

    def _build_generation_prompt(self, message: AndroidScriptGenerationMessage, script_format: str) -> str:
        """构建脚本生成提示"""
        analysis_result = message.analysis_result
        format_lower = script_format.lower()

        # 根据格式选择不同的提示模板
        if 'midscene' in format_lower or format_lower in ['vitest', 'jest']:
            return self._build_midscene_prompt(analysis_result, message, script_format)
        else:
            return self._build_traditional_prompt(analysis_result, message, script_format)

    def _build_midscene_prompt(self, analysis_result: Dict[str, Any],
                              message: AndroidScriptGenerationMessage, script_format: str) -> str:
        """构建MidScene.js格式的生成提示"""

        # 提取UI元素信息用于AI操作描述
        ui_elements = analysis_result.get('ui_elements', [])
        element_descriptions = []
        for element in ui_elements[:10]:  # 限制元素数量
            desc = f"- {element.get('description', '未知元素')}"
            if element.get('text'):
                desc += f" (文本: '{element.get('text')}')"
            element_descriptions.append(desc)

        prompt = f"""
请基于Android界面分析结果，生成{script_format}格式的MidScene.js自动化测试脚本。

## 界面分析结果
### 识别的UI元素:
{chr(10).join(element_descriptions)}

### 测试场景:
{chr(10).join(f"- {scenario}" for scenario in analysis_result.get('test_scenarios', []))}

### 分析总结:
{analysis_result.get('analysis_summary', '无分析总结')}

## 测试需求
{message.test_description}

## 脚本要求
- **优先使用MidScene.js Android SDK**
- 使用自然语言描述操作，避免复杂的元素定位
- 包含完整的设备连接和断开逻辑
- 使用aiAction、aiQuery、aiWaitFor、aiAssert等AI方法
- 添加适当的错误处理和重试机制
- 包含详细的注释说明

## 特殊要求
{message.additional_context or "无特殊要求"}

## 输出格式
请生成完整可执行的TypeScript代码，包含：
1. 必要的导入语句
2. 设备连接和配置
3. 基于AI的测试操作
4. 数据提取和验证
5. 错误处理和资源清理

请确保代码可以直接运行，并遵循MidScene.js Android SDK的最佳实践。
"""
        return prompt.strip()

    def _build_traditional_prompt(self, analysis_result: Dict[str, Any],
                                 message: AndroidScriptGenerationMessage, script_format: str) -> str:
        """构建传统框架的生成提示"""

        # 提取元素定位信息
        ui_elements = analysis_result.get('ui_elements', [])
        element_locators = []
        for element in ui_elements[:10]:
            locator_info = f"- {element.get('description', '未知元素')}"
            if element.get('resource_id'):
                locator_info += f" (ID: {element.get('resource_id')})"
            if element.get('text'):
                locator_info += f" (文本: '{element.get('text')}')"
            if element.get('class_name'):
                locator_info += f" (类名: {element.get('class_name')})"
            element_locators.append(locator_info)

        prompt = f"""
请基于Android界面分析结果，生成{script_format}格式的传统自动化测试脚本。

## 界面分析结果
### UI元素定位信息:
{chr(10).join(element_locators)}

### 测试步骤:
{chr(10).join(f"{i+1}. {step.get('description', '未知步骤')}" for i, step in enumerate(analysis_result.get('test_steps', [])))}

### 应用信息:
- 包名: {analysis_result.get('package_name', '未知')}
- Activity: {analysis_result.get('activity_name', '未知')}

## 测试需求
{message.test_description}

## 脚本要求
- 格式: {script_format}
- 使用精确的元素定位策略
- 包含完整的测试类和方法
- 添加详细注释和文档
- 包含setup和teardown方法
- 使用显式等待而非固定延时
- 包含异常处理机制

## 特殊要求
{message.additional_context or "无特殊要求"}

请生成完整可执行的{script_format}测试脚本，包含所有必要的导入语句和配置。
"""
        return prompt.strip()

    async def generate_android_script(self, analysis_result: Dict[str, Any],
                                    test_description: str = "Android自动化测试",
                                    script_formats: List[str] = None,
                                    additional_context: Optional[str] = None,
                                    session_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """直接生成Android脚本的公共方法"""
        try:
            if not session_id:
                session_id = str(uuid.uuid4())

            # 默认优先使用MidScene.js格式
            if not script_formats:
                script_formats = ["midscene", "vitest"]

            logger.info(f"开始生成Android脚本: {session_id}, 格式: {script_formats}")

            # 创建生成消息
            message = AndroidScriptGenerationMessage(
                session_id=session_id,
                requirement_id=session_id,
                analysis_result=analysis_result,
                test_description=test_description,
                generate_formats=script_formats,
                additional_context=additional_context
            )

            # 创建脚本生成智能体
            agent = self.create_assistant_agent(model_client_instance=self.model_client)

            # 生成脚本
            generated_scripts = await self._generate_scripts(agent, message)

            return generated_scripts

        except Exception as e:
            logger.error(f"Android脚本生成失败: {session_id}, 错误: {str(e)}")
            raise

    async def generate_midscene_script(self, analysis_result: Dict[str, Any],
                                     test_description: str = "Android自动化测试",
                                     script_type: str = "midscene",
                                     additional_context: Optional[str] = None,
                                     session_id: Optional[str] = None) -> Dict[str, Any]:
        """专门生成MidScene.js格式脚本的便捷方法"""
        try:
            scripts = await self.generate_android_script(
                analysis_result=analysis_result,
                test_description=test_description,
                script_formats=[script_type],
                additional_context=additional_context,
                session_id=session_id
            )

            return scripts[0] if scripts else {}

        except Exception as e:
            logger.error(f"生成MidScene.js脚本失败: {str(e)}")
            raise
