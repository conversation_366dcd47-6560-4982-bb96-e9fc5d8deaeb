
fastapi
uvicorn[standard]
pydantic
pydantic-settings


asyncio
aiofiles
httpx


sqlalchemy
alembic
pymysql


neo4j
py2neo


pymilvus


autogen-agentchat==0.6.1
autogen-core==0.6.1
autogen-ext[openai]==0.6.1


openai


# ui-tars
# transformers>=4.37.0
#torch>=2.0.0
# torchvision


pillow
opencv-python
numpy


crawl4ai>=0.6.3
playwright>=1.52.0


# marker-pdf==1.7.3
pymupdf
pytesseract


pandas
scikit-learn


redis
aioredis


loguru
prometheus-client


python-jose
passlib[bcrypt]
python-multipart


pytest
pytest-asyncio
black
isort
flake8


python-dotenv


websockets


python-multipart


python-dateutil


or<PERSON><PERSON>


requests


psutil


bcrypt


dynaconf


sse-starlette


pyyaml


Pillow>=9.0.0


asyncpg
aiomysql


typing-extensions
