# 智能应用UI元素分析系统设计方案

## 📋 方案概述

基于现有的RAG和多应用Page Object智能化测试框架，设计一个全自动的应用UI元素分析系统。该系统能够根据指定的应用包名，自动启动应用、智能遍历所有页面、深度分析UI元素，并将结构化数据存储到数据库中，同时自动触发向量化处理。

### 🎯 核心目标
- **自动化分析**: 无需人工干预，自动完成应用UI元素分析
- **智能遍历**: 采用多种策略智能遍历应用的所有页面
- **深度解析**: 提取元素的id、class、text、bounds等详细信息
- **语义理解**: 结合AI模型进行元素语义分析和描述生成
- **结构化存储**: 将分析结果存储为结构化数据
- **自动向量化**: 触发语义向量生成和存储

### 🔧 技术特点
- **多策略遍历**: 深度优先、广度优先、随机遍历等策略
- **智能去重**: 基于页面特征的智能去重机制
- **异常恢复**: 完善的异常处理和状态恢复
- **增量更新**: 支持应用版本更新时的增量分析
- **实时监控**: 分析过程的实时状态监控和进度反馈

## 🏗️ 系统架构设计

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                智能应用UI元素分析系统                            │
├─────────────────────────────────────────────────────────────────┤
│  🎯 分析控制层 (Analysis Control Layer)                        │
│  ├── 分析任务调度器 (Analysis Task Scheduler)                  │
│  ├── 应用生命周期管理 (App Lifecycle Manager)                  │
│  ├── 分析策略选择器 (Strategy Selector)                        │
│  └── 进度监控器 (Progress Monitor)                             │
├─────────────────────────────────────────────────────────────────┤
│  🤖 智能遍历层 (Intelligent Traversal Layer)                   │
│  ├── 页面遍历引擎 (Page Traversal Engine)                      │
│  │   ├── 深度优先遍历 (DFS Traversal)                          │
│  │   ├── 广度优先遍历 (BFS Traversal)                          │
│  │   ├── 随机遍历策略 (Random Traversal)                       │
│  │   └── 混合遍历策略 (Hybrid Traversal)                       │
│  ├── 页面状态检测器 (Page State Detector)                      │
│  ├── 交互元素识别器 (Interactive Element Detector)             │
│  └── 遍历路径优化器 (Path Optimizer)                           │
├─────────────────────────────────────────────────────────────────┤
│  🔍 元素分析层 (Element Analysis Layer)                        │
│  ├── UI层次结构解析器 (UI Hierarchy Parser)                    │
│  ├── 元素属性提取器 (Element Attribute Extractor)              │
│  ├── 语义分析引擎 (Semantic Analysis Engine)                   │
│  ├── 元素分类器 (Element Classifier)                           │
│  └── 布局分析器 (Layout Analyzer)                              │
├─────────────────────────────────────────────────────────────────┤
│  🧠 AI增强层 (AI Enhancement Layer)                            │
│  ├── 视觉理解模型 (Visual Understanding Model)                 │
│  ├── 文本语义分析 (Text Semantic Analysis)                     │
│  ├── 元素描述生成器 (Element Description Generator)            │
│  ├── 功能推理引擎 (Function Inference Engine)                  │
│  └── 相似元素聚类 (Similar Element Clustering)                │
├─────────────────────────────────────────────────────────────────┤
│  📊 数据处理层 (Data Processing Layer)                         │
│  ├── 数据清洗器 (Data Cleaner)                                 │
│  ├── 结构化转换器 (Structure Converter)                        │
│  ├── 去重处理器 (Deduplication Processor)                      │
│  ├── 数据验证器 (Data Validator)                               │
│  └── 增量更新处理器 (Incremental Update Processor)             │
├─────────────────────────────────────────────────────────────────┤
│  💾 存储管理层 (Storage Management Layer)                      │
│  ├── 关系数据库存储 (Relational DB Storage)                    │
│  ├── 向量数据库存储 (Vector DB Storage)                        │
│  ├── 文件存储管理 (File Storage Manager)                       │
│  ├── 缓存管理器 (Cache Manager)                                │
│  └── 备份恢复器 (Backup & Recovery)                            │
├─────────────────────────────────────────────────────────────────┤
│  🔧 设备管理层 (Device Management Layer)                       │
│  ├── 设备连接管理器 (Device Connection Manager)                │
│  ├── 应用安装器 (App Installer)                                │
│  ├── 设备状态监控 (Device Status Monitor)                      │
│  └── 性能监控器 (Performance Monitor)                          │
└─────────────────────────────────────────────────────────────────┘
```

### 核心模块设计

#### 1. 智能遍历引擎架构
```
framework/analysis/traversal/
├── core/                               # 遍历核心模块
│   ├── traversal_engine.py           # 遍历引擎主类
│   ├── page_state_manager.py         # 页面状态管理
│   ├── interaction_executor.py       # 交互执行器
│   └── path_recorder.py              # 路径记录器
├── strategies/                        # 遍历策略
│   ├── base_strategy.py              # 基础策略抽象类
│   ├── dfs_strategy.py               # 深度优先策略
│   ├── bfs_strategy.py               # 广度优先策略
│   ├── random_strategy.py            # 随机遍历策略
│   ├── hybrid_strategy.py            # 混合策略
│   └── smart_strategy.py             # 智能策略
├── detectors/                         # 检测器
│   ├── page_detector.py              # 页面检测器
│   ├── element_detector.py           # 元素检测器
│   ├── state_detector.py             # 状态检测器
│   └── change_detector.py            # 变化检测器
├── optimizers/                        # 优化器
│   ├── path_optimizer.py             # 路径优化器
│   ├── coverage_optimizer.py         # 覆盖率优化器
│   └── efficiency_optimizer.py       # 效率优化器
└── utils/                             # 工具类
    ├── screenshot_manager.py         # 截图管理
    ├── gesture_executor.py           # 手势执行
    └── wait_strategies.py            # 等待策略
```

#### 2. 元素分析引擎架构
```
framework/analysis/element/
├── core/                              # 分析核心
│   ├── element_analyzer.py           # 元素分析器
│   ├── hierarchy_parser.py           # 层次解析器
│   ├── attribute_extractor.py        # 属性提取器
│   └── semantic_analyzer.py          # 语义分析器
├── extractors/                        # 提取器
│   ├── text_extractor.py             # 文本提取器
│   ├── image_extractor.py            # 图像提取器
│   ├── layout_extractor.py           # 布局提取器
│   └── interaction_extractor.py      # 交互提取器
├── classifiers/                       # 分类器
│   ├── element_classifier.py         # 元素分类器
│   ├── function_classifier.py        # 功能分类器
│   └── importance_classifier.py      # 重要性分类器
├── generators/                        # 生成器
│   ├── description_generator.py      # 描述生成器
│   ├── locator_generator.py          # 定位器生成器
│   └── keyword_generator.py          # 关键词生成器
└── validators/                        # 验证器
    ├── data_validator.py             # 数据验证器
    ├── quality_validator.py          # 质量验证器
    └── consistency_validator.py      # 一致性验证器
```

#### 3. AI增强模块架构
```
framework/analysis/ai/
├── models/                            # AI模型
│   ├── visual_model.py               # 视觉理解模型
│   ├── text_model.py                 # 文本分析模型
│   ├── multimodal_model.py           # 多模态模型
│   └── classification_model.py       # 分类模型
├── processors/                        # 处理器
│   ├── image_processor.py            # 图像处理器
│   ├── text_processor.py             # 文本处理器
│   ├── layout_processor.py           # 布局处理器
│   └── context_processor.py          # 上下文处理器
├── enhancers/                         # 增强器
│   ├── description_enhancer.py       # 描述增强器
│   ├── semantic_enhancer.py          # 语义增强器
│   └── context_enhancer.py           # 上下文增强器
└── utils/                             # 工具类
    ├── model_loader.py               # 模型加载器
    ├── inference_engine.py           # 推理引擎
    └── result_processor.py           # 结果处理器
```

## 🔄 核心业务流程

### 1. 应用分析主流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant AC as 分析控制器
    participant AM as 应用管理器
    participant TE as 遍历引擎
    participant EA as 元素分析器
    participant AI as AI增强器
    participant DB as 数据库
    participant VDB as 向量数据库

    U->>AC: 启动分析任务(包名)
    AC->>AM: 启动应用
    AM->>AM: 检查应用状态
    AM->>AC: 应用启动完成
    
    AC->>TE: 开始页面遍历
    loop 遍历所有页面
        TE->>TE: 检测当前页面
        TE->>EA: 分析页面元素
        EA->>EA: 提取元素属性
        EA->>AI: AI语义增强
        AI->>EA: 返回增强结果
        EA->>DB: 存储元素数据
        EA->>VDB: 存储语义向量
        TE->>TE: 执行页面跳转
    end
    
    TE->>AC: 遍历完成
    AC->>U: 分析任务完成
```

### 2. 智能遍历策略流程

```mermaid
sequenceDiagram
    participant TE as 遍历引擎
    participant SS as 策略选择器
    participant PD as 页面检测器
    participant ED as 元素检测器
    participant IE as 交互执行器

    TE->>SS: 选择遍历策略
    SS->>TE: 返回策略实例
    
    loop 页面遍历循环
        TE->>PD: 检测当前页面
        PD->>TE: 返回页面信息
        TE->>ED: 检测可交互元素
        ED->>TE: 返回元素列表
        TE->>TE: 计算遍历路径
        TE->>IE: 执行交互操作
        IE->>TE: 返回操作结果
        TE->>TE: 更新遍历状态
        
        alt 发现新页面
            TE->>TE: 记录新页面
        else 页面已访问
            TE->>TE: 跳过或回退
        end
    end
```

### 3. 元素分析和AI增强流程

```mermaid
sequenceDiagram
    participant EA as 元素分析器
    participant HP as 层次解析器
    participant AE as 属性提取器
    participant AI as AI增强器
    participant DG as 描述生成器
    participant EC as 元素分类器

    EA->>HP: 解析UI层次结构
    HP->>EA: 返回元素树
    
    loop 分析每个元素
        EA->>AE: 提取元素属性
        AE->>EA: 返回属性数据
        EA->>AI: 发送元素信息
        AI->>DG: 生成元素描述
        DG->>AI: 返回描述文本
        AI->>EC: 分类元素功能
        EC->>AI: 返回分类结果
        AI->>EA: 返回增强数据
        EA->>EA: 整合分析结果
    end
```

## 💻 核心代码实现

### 1. 智能遍历引擎

#### TraversalEngine主引擎类
```python
"""
智能遍历引擎
负责应用页面的智能遍历和元素发现
"""
import asyncio
import time
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum
from loguru import logger
import uiautomator2 as u2

from framework.analysis.traversal.strategies.base_strategy import BaseTraversalStrategy
from framework.analysis.traversal.strategies.smart_strategy import SmartTraversalStrategy
from framework.analysis.traversal.detectors.page_detector import PageDetector
from framework.analysis.traversal.detectors.element_detector import ElementDetector
from framework.analysis.element.core.element_analyzer import ElementAnalyzer

class TraversalStatus(Enum):
    """遍历状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class PageState:
    """页面状态数据类"""
    page_id: str
    activity_name: str
    page_hash: str
    screenshot_path: str
    ui_hierarchy: str
    elements_count: int
    timestamp: float
    visited: bool = False

@dataclass
class TraversalConfig:
    """遍历配置"""
    max_depth: int = 10
    max_pages: int = 100
    timeout: int = 300
    screenshot_enabled: bool = True
    element_analysis_enabled: bool = True
    ai_enhancement_enabled: bool = True
    strategy_name: str = "smart"

class IntelligentTraversalEngine:
    """智能遍历引擎"""
    
    def __init__(self, device: u2.Device, config: TraversalConfig):
        """
        初始化遍历引擎
        
        Args:
            device: uiautomator2设备实例
            config: 遍历配置
        """
        self.device = device
        self.config = config
        self.status = TraversalStatus.IDLE
        
        # 核心组件
        self.page_detector = PageDetector(device)
        self.element_detector = ElementDetector(device)
        self.element_analyzer = ElementAnalyzer(device)
        
        # 状态管理
        self.visited_pages: Dict[str, PageState] = {}
        self.page_graph: Dict[str, Set[str]] = {}
        self.current_page: Optional[PageState] = None
        self.traversal_path: List[str] = []
        
        # 策略管理
        self.strategy: Optional[BaseTraversalStrategy] = None
        self._init_strategy()
        
        # 统计信息
        self.stats = {
            "pages_discovered": 0,
            "elements_analyzed": 0,
            "interactions_performed": 0,
            "errors_encountered": 0,
            "start_time": None,
            "end_time": None
        }
    
    def _init_strategy(self):
        """初始化遍历策略"""
        try:
            if self.config.strategy_name == "smart":
                self.strategy = SmartTraversalStrategy(self.device, self.config)
            else:
                # 可以根据需要添加其他策略
                self.strategy = SmartTraversalStrategy(self.device, self.config)
                
            logger.info(f"遍历策略初始化完成: {self.config.strategy_name}")
            
        except Exception as e:
            logger.error(f"遍历策略初始化失败: {str(e)}")
            raise
    
    async def start_traversal(self, package_name: str) -> Dict[str, Any]:
        """
        开始应用遍历
        
        Args:
            package_name: 应用包名
            
        Returns:
            遍历结果
        """
        try:
            logger.info(f"开始遍历应用: {package_name}")
            self.status = TraversalStatus.RUNNING
            self.stats["start_time"] = time.time()
            
            # 1. 启动应用
            await self._launch_app(package_name)
            
            # 2. 检测初始页面
            initial_page = await self._detect_current_page()
            if not initial_page:
                raise Exception("无法检测到初始页面")
            
            self.current_page = initial_page
            self.visited_pages[initial_page.page_id] = initial_page
            self.traversal_path.append(initial_page.page_id)
            
            # 3. 执行遍历策略
            await self.strategy.execute_traversal(self)
            
            # 4. 完成遍历
            self.status = TraversalStatus.COMPLETED
            self.stats["end_time"] = time.time()
            
            result = await self._generate_traversal_result()
            logger.info(f"应用遍历完成: {package_name}")
            
            return result
            
        except Exception as e:
            self.status = TraversalStatus.ERROR
            self.stats["errors_encountered"] += 1
            logger.error(f"应用遍历失败: {str(e)}")
            raise
    
    async def _launch_app(self, package_name: str):
        """启动应用"""
        try:
            # 停止应用（如果正在运行）
            self.device.app_stop(package_name)
            await asyncio.sleep(1)
            
            # 启动应用
            self.device.app_start(package_name, stop=True)
            await asyncio.sleep(3)
            
            # 验证应用是否启动成功
            current_app = self.device.app_current()
            if current_app.get("package") != package_name:
                raise Exception(f"应用启动失败: {package_name}")
            
            logger.info(f"应用启动成功: {package_name}")
            
        except Exception as e:
            logger.error(f"应用启动失败: {str(e)}")
            raise
    
    async def _detect_current_page(self) -> Optional[PageState]:
        """检测当前页面"""
        try:
            # 获取当前Activity
            current_app = self.device.app_current()
            activity_name = current_app.get("activity", "unknown")
            
            # 获取UI层次结构
            ui_hierarchy = self.device.dump_hierarchy()
            
            # 计算页面哈希
            page_hash = self.page_detector.calculate_page_hash(ui_hierarchy)
            
            # 生成页面ID
            page_id = f"{activity_name}_{page_hash[:8]}"
            
            # 截图（如果启用）
            screenshot_path = ""
            if self.config.screenshot_enabled:
                screenshot_path = await self._capture_screenshot(page_id)
            
            # 统计元素数量
            elements_count = ui_hierarchy.count("<node")
            
            page_state = PageState(
                page_id=page_id,
                activity_name=activity_name,
                page_hash=page_hash,
                screenshot_path=screenshot_path,
                ui_hierarchy=ui_hierarchy,
                elements_count=elements_count,
                timestamp=time.time()
            )
            
            logger.debug(f"检测到页面: {page_id}")
            return page_state
            
        except Exception as e:
            logger.error(f"页面检测失败: {str(e)}")
            return None
    
    async def _capture_screenshot(self, page_id: str) -> str:
        """捕获页面截图"""
        try:
            import os
            from pathlib import Path
            
            # 创建截图目录
            screenshot_dir = Path("screenshots") / "traversal"
            screenshot_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成截图文件名
            timestamp = int(time.time())
            screenshot_path = screenshot_dir / f"{page_id}_{timestamp}.png"
            
            # 捕获截图
            screenshot = self.device.screenshot(format='raw')
            with open(screenshot_path, 'wb') as f:
                f.write(screenshot)
            
            return str(screenshot_path)
            
        except Exception as e:
            logger.error(f"截图失败: {str(e)}")
            return ""
    
    async def analyze_current_page(self) -> Dict[str, Any]:
        """分析当前页面的所有元素"""
        try:
            if not self.current_page:
                return {}
            
            logger.info(f"开始分析页面: {self.current_page.page_id}")
            
            # 分析页面元素
            analysis_result = await self.element_analyzer.analyze_page(
                page_state=self.current_page,
                config={
                    "ai_enhancement": self.config.ai_enhancement_enabled,
                    "detailed_analysis": True
                }
            )
            
            # 更新统计信息
            self.stats["elements_analyzed"] += analysis_result.get("elements_count", 0)
            
            logger.info(f"页面分析完成: {self.current_page.page_id}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"页面分析失败: {str(e)}")
            return {}
    
    async def navigate_to_page(self, target_element: Dict[str, Any]) -> bool:
        """导航到目标页面"""
        try:
            # 执行交互操作
            interaction_result = await self._perform_interaction(target_element)
            if not interaction_result:
                return False
            
            # 等待页面加载
            await asyncio.sleep(2)
            
            # 检测新页面
            new_page = await self._detect_current_page()
            if not new_page:
                return False
            
            # 检查是否是新页面
            if new_page.page_id not in self.visited_pages:
                self.visited_pages[new_page.page_id] = new_page
                self.stats["pages_discovered"] += 1
                logger.info(f"发现新页面: {new_page.page_id}")
            
            # 更新当前页面
            self.current_page = new_page
            self.traversal_path.append(new_page.page_id)
            
            # 更新页面图
            if len(self.traversal_path) >= 2:
                prev_page = self.traversal_path[-2]
                if prev_page not in self.page_graph:
                    self.page_graph[prev_page] = set()
                self.page_graph[prev_page].add(new_page.page_id)
            
            return True
            
        except Exception as e:
            logger.error(f"页面导航失败: {str(e)}")
            return False
    
    async def _perform_interaction(self, element: Dict[str, Any]) -> bool:
        """执行元素交互"""
        try:
            element_type = element.get("element_type", "")
            bounds = element.get("bounds", {})
            
            if not bounds:
                return False
            
            # 计算点击坐标
            x = (bounds["left"] + bounds["right"]) // 2
            y = (bounds["top"] + bounds["bottom"]) // 2
            
            # 执行点击操作
            self.device.click(x, y)
            self.stats["interactions_performed"] += 1
            
            logger.debug(f"执行交互: {element_type} at ({x}, {y})")
            return True
            
        except Exception as e:
            logger.error(f"交互执行失败: {str(e)}")
            return False
    
    async def go_back(self) -> bool:
        """返回上一页"""
        try:
            self.device.press("back")
            await asyncio.sleep(1)
            
            # 检测当前页面
            current_page = await self._detect_current_page()
            if current_page:
                self.current_page = current_page
                self.traversal_path.append(current_page.page_id)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"返回操作失败: {str(e)}")
            return False
    
    async def _generate_traversal_result(self) -> Dict[str, Any]:
        """生成遍历结果"""
        try:
            execution_time = self.stats["end_time"] - self.stats["start_time"]
            
            result = {
                "status": self.status.value,
                "statistics": {
                    "pages_discovered": self.stats["pages_discovered"],
                    "elements_analyzed": self.stats["elements_analyzed"],
                    "interactions_performed": self.stats["interactions_performed"],
                    "errors_encountered": self.stats["errors_encountered"],
                    "execution_time": execution_time
                },
                "pages": {
                    page_id: {
                        "activity_name": page.activity_name,
                        "elements_count": page.elements_count,
                        "screenshot_path": page.screenshot_path,
                        "timestamp": page.timestamp
                    }
                    for page_id, page in self.visited_pages.items()
                },
                "page_graph": {
                    page_id: list(connections)
                    for page_id, connections in self.page_graph.items()
                },
                "traversal_path": self.traversal_path
            }
            
            return result
            
        except Exception as e:
            logger.error(f"生成遍历结果失败: {str(e)}")
            return {}
    
    def get_traversal_progress(self) -> Dict[str, Any]:
        """获取遍历进度"""
        return {
            "status": self.status.value,
            "current_page": self.current_page.page_id if self.current_page else None,
            "pages_discovered": self.stats["pages_discovered"],
            "elements_analyzed": self.stats["elements_analyzed"],
            "interactions_performed": self.stats["interactions_performed"],
            "execution_time": time.time() - self.stats["start_time"] if self.stats["start_time"] else 0
        }
```

### 2. 智能遍历策略

#### SmartTraversalStrategy智能遍历策略
```python
"""
智能遍历策略
结合多种遍历算法和AI优化的智能遍历策略
"""
import asyncio
import random
from typing import Dict, List, Any, Optional, Set
from loguru import logger

from framework.analysis.traversal.strategies.base_strategy import BaseTraversalStrategy
from framework.analysis.traversal.detectors.element_detector import ElementDetector

class SmartTraversalStrategy(BaseTraversalStrategy):
    """智能遍历策略"""
    
    def __init__(self, device, config):
        """初始化智能遍历策略"""
        super().__init__(device, config)
        self.element_detector = ElementDetector(device)
        self.visited_elements: Set[str] = set()
        self.interaction_queue: List[Dict[str, Any]] = []
        self.backtrack_stack: List[str] = []
        
    async def execute_traversal(self, engine) -> bool:
        """
        执行智能遍历
        
        Args:
            engine: 遍历引擎实例
            
        Returns:
            遍历是否成功完成
        """
        try:
            logger.info("开始执行智能遍历策略")
            
            while (len(engine.visited_pages) < self.config.max_pages and 
                   len(engine.traversal_path) < self.config.max_depth):
                
                # 分析当前页面
                await engine.analyze_current_page()
                
                # 获取可交互元素
                interactive_elements = await self._get_interactive_elements(engine)
                
                if not interactive_elements:
                    # 没有可交互元素，尝试回退
                    if not await self._backtrack(engine):
                        break
                    continue
                
                # 选择最佳交互元素
                best_element = await self._select_best_element(interactive_elements, engine)
                
                if not best_element:
                    # 没有合适的元素，尝试回退
                    if not await self._backtrack(engine):
                        break
                    continue
                
                # 记录当前页面用于回退
                if engine.current_page:
                    self.backtrack_stack.append(engine.current_page.page_id)
                
                # 执行交互
                navigation_success = await engine.navigate_to_page(best_element)
                
                if not navigation_success:
                    # 交互失败，尝试其他元素
                    self._mark_element_failed(best_element)
                    continue
                
                # 检查是否进入了新页面
                if (engine.current_page and 
                    engine.current_page.page_id not in engine.visited_pages):
                    logger.info(f"成功进入新页面: {engine.current_page.page_id}")
                else:
                    # 没有进入新页面，可能是弹窗或状态变化
                    await self._handle_state_change(engine)
            
            logger.info("智能遍历策略执行完成")
            return True
            
        except Exception as e:
            logger.error(f"智能遍历策略执行失败: {str(e)}")
            return False
    
    async def _get_interactive_elements(self, engine) -> List[Dict[str, Any]]:
        """获取当前页面的可交互元素"""
        try:
            if not engine.current_page:
                return []
            
            # 检测可交互元素
            elements = await self.element_detector.detect_interactive_elements(
                engine.current_page.ui_hierarchy
            )
            
            # 过滤已访问的元素
            filtered_elements = []
            for element in elements:
                element_id = self._generate_element_id(element)
                if element_id not in self.visited_elements:
                    filtered_elements.append(element)
            
            logger.debug(f"发现 {len(filtered_elements)} 个可交互元素")
            return filtered_elements
            
        except Exception as e:
            logger.error(f"获取可交互元素失败: {str(e)}")
            return []
    
    async def _select_best_element(self, elements: List[Dict[str, Any]], engine) -> Optional[Dict[str, Any]]:
        """选择最佳的交互元素"""
        try:
            if not elements:
                return None
            
            # 计算每个元素的优先级分数
            scored_elements = []
            for element in elements:
                score = await self._calculate_element_score(element, engine)
                scored_elements.append((element, score))
            
            # 按分数排序
            scored_elements.sort(key=lambda x: x[1], reverse=True)
            
            # 选择最高分的元素
            best_element = scored_elements[0][0]
            
            # 记录元素已被访问
            element_id = self._generate_element_id(best_element)
            self.visited_elements.add(element_id)
            
            logger.debug(f"选择最佳元素: {best_element.get('element_type', 'unknown')}")
            return best_element
            
        except Exception as e:
            logger.error(f"选择最佳元素失败: {str(e)}")
            return None
    
    async def _calculate_element_score(self, element: Dict[str, Any], engine) -> float:
        """计算元素的优先级分数"""
        try:
            score = 0.0
            
            # 基础分数
            element_type = element.get("element_type", "")
            
            # 按元素类型给分
            type_scores = {
                "button": 0.8,
                "imagebutton": 0.7,
                "textview": 0.3,
                "edittext": 0.6,
                "checkbox": 0.5,
                "radiobutton": 0.5,
                "switch": 0.5,
                "tab": 0.9,
                "menu": 0.9
            }
            
            score += type_scores.get(element_type.lower(), 0.2)
            
            # 文本内容分析
            text = element.get("text", "").lower()
            if text:
                # 导航相关的文本优先级更高
                navigation_keywords = ["下一步", "继续", "确定", "登录", "注册", "搜索", "设置"]
                for keyword in navigation_keywords:
                    if keyword in text:
                        score += 0.3
                        break
            
            # 位置分析
            bounds = element.get("bounds", {})
            if bounds:
                # 屏幕中央的元素优先级更高
                center_x = (bounds.get("left", 0) + bounds.get("right", 0)) / 2
                center_y = (bounds.get("top", 0) + bounds.get("bottom", 0)) / 2
                
                # 假设屏幕尺寸为1080x1920
                screen_center_x, screen_center_y = 540, 960
                distance = ((center_x - screen_center_x) ** 2 + (center_y - screen_center_y) ** 2) ** 0.5
                
                # 距离越近分数越高
                score += max(0, 0.2 - distance / 2000)
            
            # 可点击性分析
            if element.get("clickable", False):
                score += 0.2
            
            # 是否已访问过类似元素
            similar_visited = self._count_similar_visited_elements(element)
            score -= similar_visited * 0.1
            
            return max(0.0, score)
            
        except Exception as e:
            logger.error(f"计算元素分数失败: {str(e)}")
            return 0.0
    
    def _generate_element_id(self, element: Dict[str, Any]) -> str:
        """生成元素唯一标识"""
        try:
            # 使用多个属性组合生成唯一ID
            resource_id = element.get("resource_id", "")
            text = element.get("text", "")
            class_name = element.get("class_name", "")
            bounds = element.get("bounds", {})
            
            # 生成简单的哈希
            id_string = f"{resource_id}_{text}_{class_name}_{bounds}"
            return str(hash(id_string))
            
        except Exception as e:
            logger.error(f"生成元素ID失败: {str(e)}")
            return str(random.randint(1000, 9999))
    
    def _count_similar_visited_elements(self, element: Dict[str, Any]) -> int:
        """统计已访问的相似元素数量"""
        try:
            count = 0
            element_type = element.get("element_type", "")
            text = element.get("text", "")
            
            for visited_id in self.visited_elements:
                # 这里可以实现更复杂的相似性判断
                # 简单实现：相同类型的元素
                if element_type in visited_id:
                    count += 1
            
            return count
            
        except Exception as e:
            logger.error(f"统计相似元素失败: {str(e)}")
            return 0
    
    def _mark_element_failed(self, element: Dict[str, Any]):
        """标记元素交互失败"""
        try:
            element_id = self._generate_element_id(element)
            self.visited_elements.add(f"failed_{element_id}")
            logger.debug(f"标记元素交互失败: {element_id}")
            
        except Exception as e:
            logger.error(f"标记元素失败状态失败: {str(e)}")
    
    async def _backtrack(self, engine) -> bool:
        """回退到上一个页面"""
        try:
            if not self.backtrack_stack:
                logger.info("没有可回退的页面")
                return False
            
            # 执行回退操作
            success = await engine.go_back()
            
            if success and self.backtrack_stack:
                self.backtrack_stack.pop()
                logger.debug("成功回退到上一页面")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"回退操作失败: {str(e)}")
            return False
    
    async def _handle_state_change(self, engine):
        """处理页面状态变化"""
        try:
            # 等待一段时间观察页面变化
            await asyncio.sleep(1)
            
            # 检测是否有弹窗或对话框
            current_hierarchy = engine.device.dump_hierarchy()
            
            # 简单的弹窗检测
            if "dialog" in current_hierarchy.lower() or "popup" in current_hierarchy.lower():
                logger.info("检测到弹窗，尝试关闭")
                # 尝试点击确定或取消按钮
                try:
                    if engine.device(text="确定").exists:
                        engine.device(text="确定").click()
                    elif engine.device(text="取消").exists:
                        engine.device(text="取消").click()
                    elif engine.device(text="关闭").exists:
                        engine.device(text="关闭").click()
                    else:
                        # 尝试按返回键
                        engine.device.press("back")
                except:
                    pass
                
                await asyncio.sleep(1)
            
        except Exception as e:
            logger.error(f"处理状态变化失败: {str(e)}")
```

### 3. 智能元素分析器

#### ElementAnalyzer元素分析器
```python
"""
智能元素分析器
深度分析UI元素的属性、功能和语义信息
"""
import json
import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from loguru import logger

from framework.analysis.ai.models.visual_model import VisualUnderstandingModel
from framework.analysis.ai.processors.text_processor import TextProcessor
from framework.analysis.element.extractors.attribute_extractor import AttributeExtractor
from framework.analysis.element.classifiers.element_classifier import ElementClassifier

@dataclass
class ElementInfo:
    """元素信息数据类"""
    element_id: str
    element_type: str
    class_name: str
    resource_id: str
    text: str
    content_desc: str
    bounds: Dict[str, int]
    clickable: bool
    scrollable: bool
    checkable: bool
    checked: bool
    enabled: bool
    focusable: bool
    focused: bool
    selected: bool
    long_clickable: bool
    password: bool

    # AI增强属性
    semantic_description: str = ""
    functional_category: str = ""
    importance_score: float = 0.0
    interaction_suggestions: List[str] = None
    semantic_keywords: List[str] = None

    def __post_init__(self):
        if self.interaction_suggestions is None:
            self.interaction_suggestions = []
        if self.semantic_keywords is None:
            self.semantic_keywords = []

class IntelligentElementAnalyzer:
    """智能元素分析器"""

    def __init__(self, device):
        """初始化元素分析器"""
        self.device = device
        self.visual_model = VisualUnderstandingModel()
        self.text_processor = TextProcessor()
        self.attribute_extractor = AttributeExtractor()
        self.element_classifier = ElementClassifier()

        # 分析缓存
        self.analysis_cache: Dict[str, Dict[str, Any]] = {}

    async def analyze_page(self, page_state, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析页面的所有元素

        Args:
            page_state: 页面状态对象
            config: 分析配置

        Returns:
            页面分析结果
        """
        try:
            logger.info(f"开始分析页面元素: {page_state.page_id}")

            # 解析UI层次结构
            elements = await self._parse_ui_hierarchy(page_state.ui_hierarchy)

            # 分析每个元素
            analyzed_elements = []
            for element_data in elements:
                element_info = await self._analyze_single_element(
                    element_data, page_state, config
                )
                if element_info:
                    analyzed_elements.append(element_info)

            # 生成页面级分析结果
            page_analysis = await self._generate_page_analysis(
                page_state, analyzed_elements, config
            )

            # 存储分析结果
            await self._store_analysis_results(page_state, analyzed_elements, page_analysis)

            result = {
                "page_id": page_state.page_id,
                "elements_count": len(analyzed_elements),
                "elements": [self._element_to_dict(elem) for elem in analyzed_elements],
                "page_analysis": page_analysis,
                "analysis_timestamp": page_state.timestamp
            }

            logger.info(f"页面元素分析完成: {page_state.page_id}, 元素数量: {len(analyzed_elements)}")
            return result

        except Exception as e:
            logger.error(f"页面元素分析失败: {str(e)}")
            return {}

    async def _parse_ui_hierarchy(self, ui_hierarchy: str) -> List[Dict[str, Any]]:
        """解析UI层次结构"""
        try:
            elements = []
            root = ET.fromstring(ui_hierarchy)

            def parse_node(node, parent_path=""):
                """递归解析节点"""
                try:
                    # 提取节点属性
                    attribs = node.attrib

                    # 解析bounds
                    bounds_str = attribs.get("bounds", "[0,0][0,0]")
                    bounds = self._parse_bounds(bounds_str)

                    # 生成元素路径
                    element_path = f"{parent_path}/{node.tag}[{len(elements)}]"

                    element_data = {
                        "index": len(elements),
                        "class_name": attribs.get("class", ""),
                        "resource_id": attribs.get("resource-id", ""),
                        "text": attribs.get("text", ""),
                        "content_desc": attribs.get("content-desc", ""),
                        "package": attribs.get("package", ""),
                        "bounds": bounds,
                        "clickable": attribs.get("clickable", "false").lower() == "true",
                        "scrollable": attribs.get("scrollable", "false").lower() == "true",
                        "checkable": attribs.get("checkable", "false").lower() == "true",
                        "checked": attribs.get("checked", "false").lower() == "true",
                        "enabled": attribs.get("enabled", "true").lower() == "true",
                        "focusable": attribs.get("focusable", "false").lower() == "true",
                        "focused": attribs.get("focused", "false").lower() == "true",
                        "selected": attribs.get("selected", "false").lower() == "true",
                        "long_clickable": attribs.get("long-clickable", "false").lower() == "true",
                        "password": attribs.get("password", "false").lower() == "true",
                        "element_path": element_path,
                        "parent_path": parent_path
                    }

                    elements.append(element_data)

                    # 递归处理子节点
                    for child in node:
                        parse_node(child, element_path)

                except Exception as e:
                    logger.warning(f"解析节点失败: {str(e)}")

            # 开始解析
            parse_node(root)

            logger.debug(f"解析UI层次结构完成，元素数量: {len(elements)}")
            return elements

        except Exception as e:
            logger.error(f"解析UI层次结构失败: {str(e)}")
            return []

    def _parse_bounds(self, bounds_str: str) -> Dict[str, int]:
        """解析bounds字符串"""
        try:
            # bounds格式: [left,top][right,bottom]
            import re
            matches = re.findall(r'\[(\d+),(\d+)\]', bounds_str)
            if len(matches) == 2:
                left, top = int(matches[0][0]), int(matches[0][1])
                right, bottom = int(matches[1][0]), int(matches[1][1])
                return {
                    "left": left,
                    "top": top,
                    "right": right,
                    "bottom": bottom,
                    "width": right - left,
                    "height": bottom - top
                }
        except Exception as e:
            logger.warning(f"解析bounds失败: {bounds_str}, 错误: {str(e)}")

        return {"left": 0, "top": 0, "right": 0, "bottom": 0, "width": 0, "height": 0}

    async def _analyze_single_element(
        self,
        element_data: Dict[str, Any],
        page_state,
        config: Dict[str, Any]
    ) -> Optional[ElementInfo]:
        """分析单个元素"""
        try:
            # 生成元素ID
            element_id = self._generate_element_id(element_data, page_state.page_id)

            # 基础属性提取
            element_info = ElementInfo(
                element_id=element_id,
                element_type=self._classify_element_type(element_data),
                class_name=element_data["class_name"],
                resource_id=element_data["resource_id"],
                text=element_data["text"],
                content_desc=element_data["content_desc"],
                bounds=element_data["bounds"],
                clickable=element_data["clickable"],
                scrollable=element_data["scrollable"],
                checkable=element_data["checkable"],
                checked=element_data["checked"],
                enabled=element_data["enabled"],
                focusable=element_data["focusable"],
                focused=element_data["focused"],
                selected=element_data["selected"],
                long_clickable=element_data["long_clickable"],
                password=element_data["password"]
            )

            # AI增强分析
            if config.get("ai_enhancement", False):
                await self._enhance_element_with_ai(element_info, element_data, page_state)

            return element_info

        except Exception as e:
            logger.error(f"分析单个元素失败: {str(e)}")
            return None

    def _generate_element_id(self, element_data: Dict[str, Any], page_id: str) -> str:
        """生成元素唯一ID"""
        try:
            # 使用多个属性组合生成唯一ID
            resource_id = element_data.get("resource_id", "")
            class_name = element_data.get("class_name", "")
            text = element_data.get("text", "")
            bounds = element_data.get("bounds", {})
            index = element_data.get("index", 0)

            # 生成哈希
            id_components = [
                page_id,
                resource_id,
                class_name,
                text,
                str(bounds.get("left", 0)),
                str(bounds.get("top", 0)),
                str(index)
            ]

            id_string = "_".join(filter(None, id_components))
            element_id = f"elem_{abs(hash(id_string))}"

            return element_id

        except Exception as e:
            logger.error(f"生成元素ID失败: {str(e)}")
            return f"elem_{element_data.get('index', 0)}"

    def _classify_element_type(self, element_data: Dict[str, Any]) -> str:
        """分类元素类型"""
        try:
            class_name = element_data.get("class_name", "").lower()
            text = element_data.get("text", "")
            content_desc = element_data.get("content_desc", "")

            # 基于class name的分类
            if "button" in class_name:
                return "button"
            elif "edittext" in class_name or "edit" in class_name:
                return "input"
            elif "textview" in class_name or "text" in class_name:
                return "text"
            elif "imageview" in class_name or "image" in class_name:
                return "image"
            elif "checkbox" in class_name:
                return "checkbox"
            elif "radiobutton" in class_name:
                return "radio"
            elif "switch" in class_name:
                return "switch"
            elif "seekbar" in class_name or "slider" in class_name:
                return "slider"
            elif "progressbar" in class_name or "progress" in class_name:
                return "progress"
            elif "recyclerview" in class_name or "listview" in class_name:
                return "list"
            elif "viewpager" in class_name or "pager" in class_name:
                return "pager"
            elif "toolbar" in class_name:
                return "toolbar"
            elif "tab" in class_name:
                return "tab"

            # 基于属性的分类
            if element_data.get("clickable", False):
                if text or content_desc:
                    return "clickable_text"
                else:
                    return "clickable_element"
            elif element_data.get("scrollable", False):
                return "scrollable_container"
            elif element_data.get("checkable", False):
                return "checkable_element"

            # 默认分类
            return "unknown"

        except Exception as e:
            logger.error(f"元素类型分类失败: {str(e)}")
            return "unknown"

    async def _enhance_element_with_ai(
        self,
        element_info: ElementInfo,
        element_data: Dict[str, Any],
        page_state
    ):
        """使用AI增强元素信息"""
        try:
            # 生成语义描述
            semantic_description = await self._generate_semantic_description(
                element_info, element_data, page_state
            )
            element_info.semantic_description = semantic_description

            # 功能分类
            functional_category = await self._classify_element_function(
                element_info, element_data
            )
            element_info.functional_category = functional_category

            # 计算重要性分数
            importance_score = await self._calculate_importance_score(
                element_info, element_data
            )
            element_info.importance_score = importance_score

            # 生成交互建议
            interaction_suggestions = await self._generate_interaction_suggestions(
                element_info, element_data
            )
            element_info.interaction_suggestions = interaction_suggestions

            # 提取语义关键词
            semantic_keywords = await self._extract_semantic_keywords(
                element_info, element_data
            )
            element_info.semantic_keywords = semantic_keywords

        except Exception as e:
            logger.error(f"AI增强元素信息失败: {str(e)}")

    async def _generate_semantic_description(
        self,
        element_info: ElementInfo,
        element_data: Dict[str, Any],
        page_state
    ) -> str:
        """生成元素的语义描述"""
        try:
            # 构建上下文信息
            context = {
                "element_type": element_info.element_type,
                "class_name": element_info.class_name,
                "text": element_info.text,
                "content_desc": element_info.content_desc,
                "resource_id": element_info.resource_id,
                "clickable": element_info.clickable,
                "bounds": element_info.bounds,
                "page_activity": page_state.activity_name
            }

            # 使用文本处理器生成描述
            description = await self.text_processor.generate_element_description(context)

            return description or "未知元素"

        except Exception as e:
            logger.error(f"生成语义描述失败: {str(e)}")
            return "未知元素"

    async def _classify_element_function(
        self,
        element_info: ElementInfo,
        element_data: Dict[str, Any]
    ) -> str:
        """分类元素功能"""
        try:
            # 使用元素分类器
            function_category = await self.element_classifier.classify_function(
                element_type=element_info.element_type,
                text=element_info.text,
                content_desc=element_info.content_desc,
                resource_id=element_info.resource_id,
                clickable=element_info.clickable
            )

            return function_category or "general"

        except Exception as e:
            logger.error(f"元素功能分类失败: {str(e)}")
            return "general"

    async def _calculate_importance_score(
        self,
        element_info: ElementInfo,
        element_data: Dict[str, Any]
    ) -> float:
        """计算元素重要性分数"""
        try:
            score = 0.0

            # 基于元素类型的基础分数
            type_scores = {
                "button": 0.8,
                "input": 0.7,
                "clickable_text": 0.6,
                "tab": 0.9,
                "checkbox": 0.5,
                "radio": 0.5,
                "text": 0.2,
                "image": 0.3
            }

            score += type_scores.get(element_info.element_type, 0.1)

            # 基于交互性的分数
            if element_info.clickable:
                score += 0.3
            if element_info.focusable:
                score += 0.2
            if element_info.scrollable:
                score += 0.2

            # 基于文本内容的分数
            text = element_info.text.lower()
            important_keywords = [
                "登录", "注册", "确定", "取消", "搜索", "设置",
                "保存", "删除", "编辑", "添加", "购买", "支付"
            ]

            for keyword in important_keywords:
                if keyword in text:
                    score += 0.2
                    break

            # 基于位置的分数（屏幕中央和顶部更重要）
            bounds = element_info.bounds
            if bounds["width"] > 0 and bounds["height"] > 0:
                center_y = (bounds["top"] + bounds["bottom"]) / 2
                if center_y < 500:  # 屏幕上半部分
                    score += 0.1

            return min(1.0, score)

        except Exception as e:
            logger.error(f"计算重要性分数失败: {str(e)}")
            return 0.0

    async def _generate_interaction_suggestions(
        self,
        element_info: ElementInfo,
        element_data: Dict[str, Any]
    ) -> List[str]:
        """生成交互建议"""
        try:
            suggestions = []

            if element_info.clickable:
                suggestions.append("click")

            if element_info.long_clickable:
                suggestions.append("long_click")

            if element_info.element_type == "input":
                suggestions.extend(["input_text", "clear_text"])

            if element_info.scrollable:
                suggestions.extend(["scroll_up", "scroll_down", "scroll_left", "scroll_right"])

            if element_info.checkable:
                suggestions.extend(["check", "uncheck"])

            if element_info.element_type in ["slider", "seekbar"]:
                suggestions.extend(["drag", "set_value"])

            return suggestions

        except Exception as e:
            logger.error(f"生成交互建议失败: {str(e)}")
            return []

    async def _extract_semantic_keywords(
        self,
        element_info: ElementInfo,
        element_data: Dict[str, Any]
    ) -> List[str]:
        """提取语义关键词"""
        try:
            keywords = []

            # 从文本中提取关键词
            text_keywords = await self.text_processor.extract_keywords(
                element_info.text + " " + element_info.content_desc
            )
            keywords.extend(text_keywords)

            # 从resource_id中提取关键词
            if element_info.resource_id:
                id_parts = element_info.resource_id.split("/")[-1].split("_")
                keywords.extend(id_parts)

            # 添加元素类型关键词
            keywords.append(element_info.element_type)

            # 添加功能关键词
            if element_info.functional_category:
                keywords.append(element_info.functional_category)

            # 去重并过滤
            keywords = list(set(filter(lambda x: len(x) > 1, keywords)))

            return keywords[:10]  # 限制关键词数量

        except Exception as e:
            logger.error(f"提取语义关键词失败: {str(e)}")
            return []

    async def _generate_page_analysis(
        self,
        page_state,
        elements: List[ElementInfo],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成页面级分析结果"""
        try:
            # 统计信息
            total_elements = len(elements)
            interactive_elements = len([e for e in elements if e.clickable or e.focusable])
            input_elements = len([e for e in elements if e.element_type == "input"])
            button_elements = len([e for e in elements if e.element_type == "button"])

            # 功能分类统计
            function_stats = {}
            for element in elements:
                func = element.functional_category
                function_stats[func] = function_stats.get(func, 0) + 1

            # 重要元素识别
            important_elements = [
                e for e in elements
                if e.importance_score > 0.7
            ]

            page_analysis = {
                "page_id": page_state.page_id,
                "activity_name": page_state.activity_name,
                "statistics": {
                    "total_elements": total_elements,
                    "interactive_elements": interactive_elements,
                    "input_elements": input_elements,
                    "button_elements": button_elements
                },
                "function_distribution": function_stats,
                "important_elements": [
                    {
                        "element_id": e.element_id,
                        "element_type": e.element_type,
                        "text": e.text,
                        "importance_score": e.importance_score
                    }
                    for e in important_elements
                ],
                "page_complexity": self._calculate_page_complexity(elements),
                "accessibility_score": self._calculate_accessibility_score(elements)
            }

            return page_analysis

        except Exception as e:
            logger.error(f"生成页面分析失败: {str(e)}")
            return {}

    def _calculate_page_complexity(self, elements: List[ElementInfo]) -> float:
        """计算页面复杂度"""
        try:
            # 基于元素数量和类型多样性计算复杂度
            total_elements = len(elements)
            unique_types = len(set(e.element_type for e in elements))
            interactive_ratio = len([e for e in elements if e.clickable]) / max(1, total_elements)

            complexity = (total_elements / 100) * 0.4 + (unique_types / 10) * 0.3 + interactive_ratio * 0.3

            return min(1.0, complexity)

        except Exception as e:
            logger.error(f"计算页面复杂度失败: {str(e)}")
            return 0.0

    def _calculate_accessibility_score(self, elements: List[ElementInfo]) -> float:
        """计算页面可访问性分数"""
        try:
            total_elements = len(elements)
            if total_elements == 0:
                return 0.0

            # 有描述文本的元素
            described_elements = len([
                e for e in elements
                if e.text or e.content_desc
            ])

            # 可聚焦的元素
            focusable_elements = len([e for e in elements if e.focusable])

            # 计算分数
            description_score = described_elements / total_elements
            focus_score = focusable_elements / max(1, len([e for e in elements if e.clickable]))

            accessibility_score = (description_score * 0.6 + focus_score * 0.4)

            return accessibility_score

        except Exception as e:
            logger.error(f"计算可访问性分数失败: {str(e)}")
            return 0.0

    def _element_to_dict(self, element: ElementInfo) -> Dict[str, Any]:
        """将元素信息转换为字典"""
        return {
            "element_id": element.element_id,
            "element_type": element.element_type,
            "class_name": element.class_name,
            "resource_id": element.resource_id,
            "text": element.text,
            "content_desc": element.content_desc,
            "bounds": element.bounds,
            "clickable": element.clickable,
            "scrollable": element.scrollable,
            "checkable": element.checkable,
            "checked": element.checked,
            "enabled": element.enabled,
            "focusable": element.focusable,
            "focused": element.focused,
            "selected": element.selected,
            "long_clickable": element.long_clickable,
            "password": element.password,
            "semantic_description": element.semantic_description,
            "functional_category": element.functional_category,
            "importance_score": element.importance_score,
            "interaction_suggestions": element.interaction_suggestions,
            "semantic_keywords": element.semantic_keywords
        }

    async def _store_analysis_results(
        self,
        page_state,
        elements: List[ElementInfo],
        page_analysis: Dict[str, Any]
    ):
        """存储分析结果到数据库"""
        try:
            from framework.storage.database_manager import DatabaseManager
            from framework.storage.vector_manager import VectorManager

            db_manager = DatabaseManager()
            vector_manager = VectorManager()

            # 存储页面信息
            await db_manager.store_page_analysis(page_state, page_analysis)

            # 存储元素信息
            for element in elements:
                await db_manager.store_element_analysis(element)

                # 生成并存储语义向量
                if element.semantic_description:
                    await vector_manager.store_element_vector(
                        element.element_id,
                        element.semantic_description,
                        {
                            "element_type": element.element_type,
                            "functional_category": element.functional_category,
                            "importance_score": element.importance_score,
                            "keywords": element.semantic_keywords
                        }
                    )

            logger.info(f"分析结果存储完成: {page_state.page_id}")

        except Exception as e:
            logger.error(f"存储分析结果失败: {str(e)}")
```

### 4. AI增强模块

#### TextProcessor文本处理器
```python
"""
文本处理器
提供文本分析、关键词提取、描述生成等功能
"""
import re
import jieba
from typing import List, Dict, Any, Optional
from loguru import logger

class TextProcessor:
    """文本处理器"""

    def __init__(self):
        """初始化文本处理器"""
        # 初始化jieba分词
        jieba.initialize()

        # 停用词列表
        self.stop_words = {
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个",
            "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好",
            "自己", "这", "那", "它", "他", "她", "们", "个", "中", "可以", "这个", "那个"
        }

        # 元素描述模板
        self.description_templates = {
            "button": "可点击的{text}按钮",
            "input": "用于输入{hint}的文本框",
            "text": "显示{text}的文本",
            "image": "图片元素",
            "checkbox": "可选择的{text}复选框",
            "radio": "单选按钮{text}",
            "switch": "开关控件{text}",
            "tab": "标签页{text}",
            "list": "列表容器",
            "toolbar": "工具栏",
            "unknown": "未知类型的界面元素"
        }

    async def generate_element_description(self, context: Dict[str, Any]) -> str:
        """
        生成元素描述

        Args:
            context: 元素上下文信息

        Returns:
            元素描述文本
        """
        try:
            element_type = context.get("element_type", "unknown")
            text = context.get("text", "")
            content_desc = context.get("content_desc", "")
            resource_id = context.get("resource_id", "")
            clickable = context.get("clickable", False)

            # 获取描述模板
            template = self.description_templates.get(element_type, self.description_templates["unknown"])

            # 确定显示文本
            display_text = text or content_desc
            if not display_text and resource_id:
                # 从resource_id提取有意义的文本
                display_text = self._extract_text_from_resource_id(resource_id)

            # 生成描述
            if display_text:
                description = template.format(text=display_text, hint=display_text)
            else:
                description = template.format(text="", hint="")

            # 添加交互性描述
            if clickable and element_type not in ["button"]:
                description += "，可点击"

            # 清理描述
            description = self._clean_description(description)

            return description

        except Exception as e:
            logger.error(f"生成元素描述失败: {str(e)}")
            return "界面元素"

    def _extract_text_from_resource_id(self, resource_id: str) -> str:
        """从resource_id提取有意义的文本"""
        try:
            if not resource_id:
                return ""

            # 提取最后一部分
            parts = resource_id.split("/")
            if len(parts) > 1:
                name = parts[-1]
            else:
                name = resource_id

            # 分割下划线和驼峰命名
            words = re.split(r'[_\-]|(?=[A-Z])', name)

            # 过滤和清理
            meaningful_words = []
            for word in words:
                word = word.lower().strip()
                if len(word) > 1 and word not in self.stop_words:
                    meaningful_words.append(word)

            return " ".join(meaningful_words[:3])  # 限制词数

        except Exception as e:
            logger.error(f"从resource_id提取文本失败: {str(e)}")
            return ""

    def _clean_description(self, description: str) -> str:
        """清理描述文本"""
        try:
            # 移除多余的空格和标点
            description = re.sub(r'\s+', ' ', description)
            description = description.replace("的的", "的")
            description = description.replace("，，", "，")
            description = description.strip("，。")

            return description.strip()

        except Exception as e:
            logger.error(f"清理描述文本失败: {str(e)}")
            return description

    async def extract_keywords(self, text: str) -> List[str]:
        """
        提取关键词

        Args:
            text: 输入文本

        Returns:
            关键词列表
        """
        try:
            if not text:
                return []

            # 清理文本
            cleaned_text = self._clean_text(text)

            # 分词
            words = jieba.lcut(cleaned_text)

            # 过滤关键词
            keywords = []
            for word in words:
                word = word.strip()
                if (len(word) > 1 and
                    word not in self.stop_words and
                    not word.isdigit() and
                    re.match(r'^[\u4e00-\u9fa5a-zA-Z]+$', word)):
                    keywords.append(word)

            # 去重并限制数量
            keywords = list(set(keywords))[:10]

            return keywords

        except Exception as e:
            logger.error(f"提取关键词失败: {str(e)}")
            return []

    def _clean_text(self, text: str) -> str:
        """清理文本"""
        try:
            # 移除特殊字符
            text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)

            # 移除多余空格
            text = re.sub(r'\s+', ' ', text)

            return text.strip()

        except Exception as e:
            logger.error(f"清理文本失败: {str(e)}")
            return text

    async def analyze_text_sentiment(self, text: str) -> Dict[str, Any]:
        """
        分析文本情感

        Args:
            text: 输入文本

        Returns:
            情感分析结果
        """
        try:
            # 简单的情感分析实现
            positive_words = ["好", "棒", "优秀", "成功", "完成", "确定", "同意"]
            negative_words = ["错误", "失败", "取消", "删除", "拒绝", "禁止"]

            text_lower = text.lower()

            positive_count = sum(1 for word in positive_words if word in text_lower)
            negative_count = sum(1 for word in negative_words if word in text_lower)

            if positive_count > negative_count:
                sentiment = "positive"
                score = 0.6 + (positive_count - negative_count) * 0.1
            elif negative_count > positive_count:
                sentiment = "negative"
                score = 0.4 - (negative_count - positive_count) * 0.1
            else:
                sentiment = "neutral"
                score = 0.5

            return {
                "sentiment": sentiment,
                "score": max(0.0, min(1.0, score)),
                "positive_words": positive_count,
                "negative_words": negative_count
            }

        except Exception as e:
            logger.error(f"文本情感分析失败: {str(e)}")
            return {"sentiment": "neutral", "score": 0.5}
```

### 5. 数据存储管理器

#### DatabaseManager数据库管理器
```python
"""
数据库管理器
负责分析结果的结构化存储和管理
"""
import uuid
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from loguru import logger

from framework.database.models import AppInfo, PageInfo, ElementInfo, AnalysisTask
from framework.database.session import get_db_session
from framework.analysis.element.core.element_analyzer import ElementInfo as AnalyzedElement

class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        """初始化数据库管理器"""
        self.session_factory = get_db_session

    async def store_analysis_task(self, task_config: Dict[str, Any]) -> str:
        """
        存储分析任务

        Args:
            task_config: 任务配置

        Returns:
            任务ID
        """
        try:
            with self.session_factory() as session:
                task_id = str(uuid.uuid4())

                analysis_task = AnalysisTask(
                    id=task_id,
                    package_name=task_config["package_name"],
                    app_id=task_config.get("app_id"),
                    task_status="running",
                    config=json.dumps(task_config),
                    created_at=datetime.now()
                )

                session.add(analysis_task)
                session.commit()

                logger.info(f"分析任务已存储: {task_id}")
                return task_id

        except Exception as e:
            logger.error(f"存储分析任务失败: {str(e)}")
            raise

    async def update_task_status(self, task_id: str, status: str, result: Optional[Dict[str, Any]] = None):
        """
        更新任务状态

        Args:
            task_id: 任务ID
            status: 新状态
            result: 任务结果
        """
        try:
            with self.session_factory() as session:
                task = session.query(AnalysisTask).filter(AnalysisTask.id == task_id).first()

                if task:
                    task.task_status = status
                    task.updated_at = datetime.now()

                    if result:
                        task.result = json.dumps(result)

                    if status in ["completed", "failed"]:
                        task.completed_at = datetime.now()

                    session.commit()
                    logger.info(f"任务状态已更新: {task_id} -> {status}")

        except Exception as e:
            logger.error(f"更新任务状态失败: {str(e)}")

    async def store_page_analysis(self, page_state, page_analysis: Dict[str, Any]) -> str:
        """
        存储页面分析结果

        Args:
            page_state: 页面状态对象
            page_analysis: 页面分析结果

        Returns:
            页面记录ID
        """
        try:
            with self.session_factory() as session:
                page_id = str(uuid.uuid4())

                # 查找或创建应用记录
                app_id = await self._ensure_app_exists(
                    session,
                    page_state.activity_name.split(".")[0] if "." in page_state.activity_name else "unknown"
                )

                page_info = PageInfo(
                    id=page_id,
                    app_id=app_id,
                    page_name=page_analysis.get("activity_name", "unknown"),
                    page_class_name=page_analysis.get("activity_name", "unknown"),
                    page_type="activity",
                    page_identifier=json.dumps({
                        "activity": page_state.activity_name,
                        "hash": page_state.page_hash
                    }),
                    page_description=f"自动分析的页面: {page_state.activity_name}",
                    page_screenshot_url=page_state.screenshot_path,
                    page_hierarchy=page_state.ui_hierarchy
                )

                session.add(page_info)
                session.commit()

                logger.info(f"页面分析结果已存储: {page_id}")
                return page_id

        except Exception as e:
            logger.error(f"存储页面分析结果失败: {str(e)}")
            raise

    async def store_element_analysis(self, element: AnalyzedElement) -> bool:
        """
        存储元素分析结果

        Args:
            element: 分析后的元素对象

        Returns:
            存储是否成功
        """
        try:
            with self.session_factory() as session:
                # 查找页面ID
                page_id = await self._find_page_by_element(session, element)

                if not page_id:
                    logger.warning(f"未找到元素对应的页面: {element.element_id}")
                    return False

                element_info = ElementInfo(
                    id=element.element_id,
                    page_id=page_id,
                    element_name=self._generate_element_name(element),
                    element_type=self._map_element_type(element.element_type),
                    locator_strategies=json.dumps(self._generate_locator_strategies(element)),
                    element_description=element.semantic_description,
                    element_attributes=json.dumps(self._extract_element_attributes(element)),
                    business_actions=json.dumps(element.interaction_suggestions),
                    semantic_keywords=" ".join(element.semantic_keywords)
                )

                session.add(element_info)
                session.commit()

                logger.debug(f"元素分析结果已存储: {element.element_id}")
                return True

        except Exception as e:
            logger.error(f"存储元素分析结果失败: {str(e)}")
            return False

    async def _ensure_app_exists(self, session: Session, package_name: str) -> str:
        """确保应用记录存在"""
        try:
            # 查找现有应用
            app = session.query(AppInfo).filter(AppInfo.package_name == package_name).first()

            if app:
                return app.id

            # 创建新应用记录
            app_id = str(uuid.uuid4())
            app_info = AppInfo(
                id=app_id,
                app_name=f"自动分析应用_{package_name}",
                package_name=package_name,
                app_version="unknown",
                framework_version="1.0.0",
                description=f"通过UI分析自动创建的应用记录",
                status="active"
            )

            session.add(app_info)
            session.commit()

            logger.info(f"创建新应用记录: {app_id}")
            return app_id

        except Exception as e:
            logger.error(f"确保应用存在失败: {str(e)}")
            raise

    async def _find_page_by_element(self, session: Session, element: AnalyzedElement) -> Optional[str]:
        """根据元素查找页面ID"""
        try:
            # 这里需要根据实际情况实现页面查找逻辑
            # 可以通过元素ID中的页面信息来查找
            if "_" in element.element_id:
                page_part = element.element_id.split("_")[0]
                # 查找最近创建的页面
                page = session.query(PageInfo).filter(
                    PageInfo.page_name.contains(page_part)
                ).order_by(PageInfo.created_at.desc()).first()

                if page:
                    return page.id

            return None

        except Exception as e:
            logger.error(f"查找页面失败: {str(e)}")
            return None

    def _generate_element_name(self, element: AnalyzedElement) -> str:
        """生成元素名称"""
        try:
            if element.text:
                return element.text[:50]
            elif element.content_desc:
                return element.content_desc[:50]
            elif element.resource_id:
                return element.resource_id.split("/")[-1]
            else:
                return f"{element.element_type}_{element.element_id[-8:]}"

        except Exception as e:
            logger.error(f"生成元素名称失败: {str(e)}")
            return f"element_{element.element_id[-8:]}"

    def _map_element_type(self, element_type: str) -> str:
        """映射元素类型到数据库枚举"""
        type_mapping = {
            "button": "button",
            "input": "input",
            "text": "text",
            "image": "image",
            "list": "list",
            "checkbox": "other",
            "radio": "other",
            "switch": "other",
            "tab": "other",
            "toolbar": "other",
            "clickable_text": "text",
            "clickable_element": "button",
            "scrollable_container": "list",
            "checkable_element": "other",
            "unknown": "other"
        }

        return type_mapping.get(element_type, "other")

    def _generate_locator_strategies(self, element: AnalyzedElement) -> Dict[str, Any]:
        """生成定位策略"""
        strategies = {}

        # 主要定位策略
        if element.resource_id:
            strategies["primary"] = {
                "type": "resource_id",
                "value": element.resource_id
            }
        elif element.text:
            strategies["primary"] = {
                "type": "text",
                "value": element.text
            }
        elif element.content_desc:
            strategies["primary"] = {
                "type": "description",
                "value": element.content_desc
            }
        else:
            strategies["primary"] = {
                "type": "coordinates",
                "value": f"{element.bounds['left']},{element.bounds['top']}"
            }

        # 备用定位策略
        fallback_strategies = []

        if element.text and "primary" not in strategies:
            fallback_strategies.append({
                "type": "text",
                "value": element.text
            })

        if element.content_desc and "primary" not in strategies:
            fallback_strategies.append({
                "type": "description",
                "value": element.content_desc
            })

        if element.class_name:
            fallback_strategies.append({
                "type": "class_name",
                "value": element.class_name
            })

        if fallback_strategies:
            strategies["fallback"] = fallback_strategies[0]
            if len(fallback_strategies) > 1:
                strategies["alternatives"] = fallback_strategies[1:]

        return strategies

    def _extract_element_attributes(self, element: AnalyzedElement) -> Dict[str, Any]:
        """提取元素属性"""
        return {
            "class_name": element.class_name,
            "bounds": element.bounds,
            "clickable": element.clickable,
            "scrollable": element.scrollable,
            "checkable": element.checkable,
            "checked": element.checked,
            "enabled": element.enabled,
            "focusable": element.focusable,
            "focused": element.focused,
            "selected": element.selected,
            "long_clickable": element.long_clickable,
            "password": element.password,
            "functional_category": element.functional_category,
            "importance_score": element.importance_score
        }

    async def get_analysis_history(self, package_name: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取分析历史

        Args:
            package_name: 应用包名
            limit: 返回数量限制

        Returns:
            分析历史列表
        """
        try:
            with self.session_factory() as session:
                tasks = session.query(AnalysisTask).filter(
                    AnalysisTask.package_name == package_name
                ).order_by(AnalysisTask.created_at.desc()).limit(limit).all()

                history = []
                for task in tasks:
                    history.append({
                        "task_id": task.id,
                        "package_name": task.package_name,
                        "status": task.task_status,
                        "created_at": task.created_at.isoformat(),
                        "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                        "config": json.loads(task.config) if task.config else {},
                        "result": json.loads(task.result) if task.result else {}
                    })

                return history

        except Exception as e:
            logger.error(f"获取分析历史失败: {str(e)}")
            return []
```

### 6. 分析任务调度器

#### AnalysisTaskScheduler任务调度器
```python
"""
分析任务调度器
负责管理和调度UI元素分析任务
"""
import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
from loguru import logger
import uiautomator2 as u2

from framework.analysis.traversal.core.traversal_engine import IntelligentTraversalEngine, TraversalConfig
from framework.analysis.element.core.element_analyzer import IntelligentElementAnalyzer
from framework.storage.database_manager import DatabaseManager
from framework.storage.vector_manager import VectorManager

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class AnalysisTaskConfig:
    """分析任务配置"""
    package_name: str
    app_name: str = ""
    max_pages: int = 50
    max_depth: int = 8
    timeout: int = 600
    screenshot_enabled: bool = True
    ai_enhancement: bool = True
    traversal_strategy: str = "smart"
    device_serial: Optional[str] = None

class AnalysisTaskScheduler:
    """分析任务调度器"""

    def __init__(self):
        """初始化任务调度器"""
        self.db_manager = DatabaseManager()
        self.vector_manager = VectorManager()
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_results: Dict[str, Dict[str, Any]] = {}

    async def submit_analysis_task(self, config: AnalysisTaskConfig) -> str:
        """
        提交分析任务

        Args:
            config: 任务配置

        Returns:
            任务ID
        """
        try:
            logger.info(f"提交分析任务: {config.package_name}")

            # 存储任务到数据库
            task_id = await self.db_manager.store_analysis_task({
                "package_name": config.package_name,
                "app_name": config.app_name,
                "max_pages": config.max_pages,
                "max_depth": config.max_depth,
                "timeout": config.timeout,
                "screenshot_enabled": config.screenshot_enabled,
                "ai_enhancement": config.ai_enhancement,
                "traversal_strategy": config.traversal_strategy,
                "device_serial": config.device_serial
            })

            # 创建并启动异步任务
            task = asyncio.create_task(self._execute_analysis_task(task_id, config))
            self.running_tasks[task_id] = task

            logger.info(f"分析任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交分析任务失败: {str(e)}")
            raise

    async def _execute_analysis_task(self, task_id: str, config: AnalysisTaskConfig):
        """
        执行分析任务

        Args:
            task_id: 任务ID
            config: 任务配置
        """
        try:
            logger.info(f"开始执行分析任务: {task_id}")

            # 更新任务状态为运行中
            await self.db_manager.update_task_status(task_id, TaskStatus.RUNNING.value)

            # 连接设备
            device = await self._connect_device(config.device_serial)
            if not device:
                raise Exception("设备连接失败")

            # 创建遍历配置
            traversal_config = TraversalConfig(
                max_depth=config.max_depth,
                max_pages=config.max_pages,
                timeout=config.timeout,
                screenshot_enabled=config.screenshot_enabled,
                element_analysis_enabled=True,
                ai_enhancement_enabled=config.ai_enhancement,
                strategy_name=config.traversal_strategy
            )

            # 创建遍历引擎
            traversal_engine = IntelligentTraversalEngine(device, traversal_config)

            # 执行应用遍历和分析
            result = await traversal_engine.start_traversal(config.package_name)

            # 存储任务结果
            self.task_results[task_id] = result

            # 更新任务状态为完成
            await self.db_manager.update_task_status(
                task_id,
                TaskStatus.COMPLETED.value,
                result
            )

            logger.info(f"分析任务执行完成: {task_id}")

        except Exception as e:
            logger.error(f"分析任务执行失败: {task_id}, 错误: {str(e)}")

            # 更新任务状态为失败
            await self.db_manager.update_task_status(
                task_id,
                TaskStatus.FAILED.value,
                {"error": str(e)}
            )

        finally:
            # 清理运行中的任务
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]

    async def _connect_device(self, device_serial: Optional[str] = None) -> Optional[u2.Device]:
        """
        连接设备

        Args:
            device_serial: 设备序列号

        Returns:
            设备实例
        """
        try:
            if device_serial:
                device = u2.connect(device_serial)
            else:
                device = u2.connect()

            # 测试设备连接
            device_info = device.info
            logger.info(f"设备连接成功: {device_info.get('displayWidth', 0)}x{device_info.get('displayHeight', 0)}")

            return device

        except Exception as e:
            logger.error(f"设备连接失败: {str(e)}")
            return None

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态信息
        """
        try:
            # 检查是否在运行中
            if task_id in self.running_tasks:
                task = self.running_tasks[task_id]
                return {
                    "task_id": task_id,
                    "status": TaskStatus.RUNNING.value,
                    "is_running": not task.done(),
                    "progress": await self._get_task_progress(task_id)
                }

            # 检查任务结果
            if task_id in self.task_results:
                return {
                    "task_id": task_id,
                    "status": TaskStatus.COMPLETED.value,
                    "is_running": False,
                    "result": self.task_results[task_id]
                }

            # 从数据库查询
            # 这里需要实现数据库查询逻辑
            return {
                "task_id": task_id,
                "status": TaskStatus.PENDING.value,
                "is_running": False
            }

        except Exception as e:
            logger.error(f"获取任务状态失败: {str(e)}")
            return {
                "task_id": task_id,
                "status": TaskStatus.FAILED.value,
                "error": str(e)
            }

    async def _get_task_progress(self, task_id: str) -> Dict[str, Any]:
        """获取任务进度"""
        try:
            # 这里需要实现进度获取逻辑
            # 可以通过遍历引擎的进度接口获取
            return {
                "pages_discovered": 0,
                "elements_analyzed": 0,
                "current_page": "unknown",
                "execution_time": 0
            }

        except Exception as e:
            logger.error(f"获取任务进度失败: {str(e)}")
            return {}

    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            取消是否成功
        """
        try:
            if task_id in self.running_tasks:
                task = self.running_tasks[task_id]
                task.cancel()

                # 更新任务状态
                await self.db_manager.update_task_status(
                    task_id,
                    TaskStatus.CANCELLED.value
                )

                logger.info(f"任务已取消: {task_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"取消任务失败: {str(e)}")
            return False

    async def list_running_tasks(self) -> List[Dict[str, Any]]:
        """
        列出运行中的任务

        Returns:
            运行中的任务列表
        """
        try:
            running_tasks = []

            for task_id, task in self.running_tasks.items():
                if not task.done():
                    progress = await self._get_task_progress(task_id)
                    running_tasks.append({
                        "task_id": task_id,
                        "status": TaskStatus.RUNNING.value,
                        "progress": progress
                    })

            return running_tasks

        except Exception as e:
            logger.error(f"列出运行中任务失败: {str(e)}")
            return []

    async def cleanup_completed_tasks(self):
        """清理已完成的任务"""
        try:
            completed_tasks = []

            for task_id, task in list(self.running_tasks.items()):
                if task.done():
                    completed_tasks.append(task_id)

            for task_id in completed_tasks:
                del self.running_tasks[task_id]

            logger.info(f"清理了 {len(completed_tasks)} 个已完成的任务")

        except Exception as e:
            logger.error(f"清理已完成任务失败: {str(e)}")
```

### 7. 完整使用示例

#### 使用示例和API接口
```python
"""
智能应用UI元素分析系统使用示例
演示如何使用系统进行应用分析
"""
import asyncio
from fastapi import FastAPI, HTTPException, BackgroundTasks
from typing import Dict, Any
from loguru import logger

from framework.analysis.scheduler.analysis_task_scheduler import AnalysisTaskScheduler, AnalysisTaskConfig

# 创建FastAPI应用
app = FastAPI(title="智能应用UI元素分析系统")

# 创建任务调度器
scheduler = AnalysisTaskScheduler()

@app.post("/analysis/start")
async def start_analysis(request: Dict[str, Any]) -> Dict[str, Any]:
    """
    启动应用分析

    请求参数:
    - package_name: 应用包名 (必需)
    - app_name: 应用名称 (可选)
    - max_pages: 最大页面数 (默认50)
    - max_depth: 最大深度 (默认8)
    - timeout: 超时时间秒 (默认600)
    - screenshot_enabled: 是否启用截图 (默认true)
    - ai_enhancement: 是否启用AI增强 (默认true)
    - traversal_strategy: 遍历策略 (默认smart)
    """
    try:
        # 验证必需参数
        if "package_name" not in request:
            raise HTTPException(status_code=400, detail="package_name is required")

        # 创建任务配置
        config = AnalysisTaskConfig(
            package_name=request["package_name"],
            app_name=request.get("app_name", ""),
            max_pages=request.get("max_pages", 50),
            max_depth=request.get("max_depth", 8),
            timeout=request.get("timeout", 600),
            screenshot_enabled=request.get("screenshot_enabled", True),
            ai_enhancement=request.get("ai_enhancement", True),
            traversal_strategy=request.get("traversal_strategy", "smart"),
            device_serial=request.get("device_serial")
        )

        # 提交分析任务
        task_id = await scheduler.submit_analysis_task(config)

        return {
            "success": True,
            "task_id": task_id,
            "message": f"分析任务已启动: {config.package_name}",
            "config": {
                "package_name": config.package_name,
                "max_pages": config.max_pages,
                "max_depth": config.max_depth,
                "ai_enhancement": config.ai_enhancement
            }
        }

    except Exception as e:
        logger.error(f"启动分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/analysis/status/{task_id}")
async def get_analysis_status(task_id: str) -> Dict[str, Any]:
    """获取分析任务状态"""
    try:
        status = await scheduler.get_task_status(task_id)
        return {
            "success": True,
            "status": status
        }

    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/analysis/cancel/{task_id}")
async def cancel_analysis(task_id: str) -> Dict[str, Any]:
    """取消分析任务"""
    try:
        success = await scheduler.cancel_task(task_id)

        if success:
            return {
                "success": True,
                "message": f"任务已取消: {task_id}"
            }
        else:
            return {
                "success": False,
                "message": f"任务取消失败: {task_id}"
            }

    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/analysis/running")
async def list_running_tasks() -> Dict[str, Any]:
    """列出运行中的任务"""
    try:
        tasks = await scheduler.list_running_tasks()
        return {
            "success": True,
            "running_tasks": tasks,
            "count": len(tasks)
        }

    except Exception as e:
        logger.error(f"列出运行中任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 使用示例函数
async def example_usage():
    """使用示例"""

    # 示例1: 分析微信应用
    print("🚀 开始分析微信应用...")

    wechat_config = AnalysisTaskConfig(
        package_name="com.tencent.mm",
        app_name="微信",
        max_pages=30,
        max_depth=6,
        timeout=300,
        ai_enhancement=True,
        traversal_strategy="smart"
    )

    task_id = await scheduler.submit_analysis_task(wechat_config)
    print(f"微信分析任务ID: {task_id}")

    # 监控任务进度
    while True:
        status = await scheduler.get_task_status(task_id)
        print(f"任务状态: {status['status']}")

        if status["status"] in ["completed", "failed", "cancelled"]:
            break

        await asyncio.sleep(10)

    print("微信应用分析完成!")

    # 示例2: 批量分析多个应用
    print("\n📦 开始批量分析...")

    apps_to_analyze = [
        {"package_name": "com.taobao.taobao", "app_name": "淘宝"},
        {"package_name": "com.jingdong.app.mall", "app_name": "京东"},
        {"package_name": "com.sina.weibo", "app_name": "微博"}
    ]

    batch_tasks = []
    for app in apps_to_analyze:
        config = AnalysisTaskConfig(
            package_name=app["package_name"],
            app_name=app["app_name"],
            max_pages=20,
            max_depth=5,
            ai_enhancement=True
        )

        task_id = await scheduler.submit_analysis_task(config)
        batch_tasks.append(task_id)
        print(f"提交分析任务: {app['app_name']} -> {task_id}")

    # 等待所有任务完成
    print("等待批量分析完成...")
    completed_count = 0

    while completed_count < len(batch_tasks):
        completed_count = 0

        for task_id in batch_tasks:
            status = await scheduler.get_task_status(task_id)
            if status["status"] in ["completed", "failed", "cancelled"]:
                completed_count += 1

        print(f"进度: {completed_count}/{len(batch_tasks)}")
        await asyncio.sleep(15)

    print("批量分析完成!")

if __name__ == "__main__":
    # 运行使用示例
    asyncio.run(example_usage())
```

---

*本设计方案提供了完整的智能应用UI元素分析系统，包括智能遍历引擎、深度元素分析器、AI增强模块、数据存储管理器、任务调度器和完整的API接口。系统能够自动启动指定应用、智能遍历所有页面、深度分析UI元素的属性和语义信息，并将结构化数据存储到数据库中，同时自动触发向量化处理。通过RESTful API和任务调度机制，提供了完整的自动化分析解决方案，为后续的智能测试脚本生成提供高质量的数据基础。*
