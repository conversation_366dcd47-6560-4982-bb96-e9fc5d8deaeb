# 基于Page Object模式的Android自动化测试优化方案

## 📋 方案概述

基于现有的自然语言用例转Android自动化脚本技术方案，采用Page Object模式、底层框架封装、数据驱动测试等UI自动化最佳实践，构建更加健壮、可维护、可扩展的Android自动化测试框架。

### 🎯 优化目标
- **可维护性**: 通过Page Object模式实现UI元素与测试逻辑分离
- **可复用性**: 封装通用操作和组件，提高代码复用率
- **可扩展性**: 模块化设计，支持快速扩展新功能和页面
- **稳定性**: 智能等待、重试机制、异常处理提升测试稳定性
- **可读性**: 业务语言描述测试步骤，降低维护成本

### 🔧 核心优化策略
- **Page Object模式**: 页面对象封装UI元素和操作
- **组件化设计**: 通用组件抽象和复用
- **数据驱动测试**: 测试数据与测试逻辑分离
- **智能等待策略**: 自适应等待和元素状态检测
- **分层架构**: 清晰的分层设计和职责分离

## 🏗️ 优化架构设计

### 整体架构分层

```
┌─────────────────────────────────────────────────────────────────┐
│                Android自动化测试优化架构                        │
├─────────────────────────────────────────────────────────────────┤
│  🎯 业务测试层 (Business Test Layer)                           │
│  ├── 自然语言用例 (Natural Language Test Cases)                │
│  ├── 业务流程测试 (Business Flow Tests)                        │
│  └── 端到端测试 (End-to-End Tests)                             │
├─────────────────────────────────────────────────────────────────┤
│  📄 页面对象层 (Page Object Layer)                             │
│  ├── 页面对象 (Page Objects)                                   │
│  │   ├── LoginPage, HomePage, ProductPage...                   │
│  ├── 页面组件 (Page Components)                                │
│  │   ├── NavigationBar, SearchBox, ProductCard...              │
│  └── 页面工厂 (Page Factory)                                   │
├─────────────────────────────────────────────────────────────────┤
│  🔧 操作封装层 (Action Wrapper Layer)                          │
│  ├── 基础操作 (Basic Actions)                                  │
│  │   ├── ClickAction, InputAction, SwipeAction...              │
│  ├── 复合操作 (Composite Actions)                              │
│  │   ├── LoginAction, SearchAction, PurchaseAction...          │
│  └── 手势操作 (Gesture Actions)                                │
├─────────────────────────────────────────────────────────────────┤
│  🎛️ 元素管理层 (Element Management Layer)                      │
│  ├── 元素定位器 (Element Locators)                             │
│  ├── 智能等待 (Smart Waits)                                    │
│  ├── 元素缓存 (Element Cache)                                  │
│  └── 定位策略 (Locator Strategies)                             │
├─────────────────────────────────────────────────────────────────┤
│  🔌 驱动适配层 (Driver Adapter Layer)                          │
│  ├── uiautomator2适配器 (UIAutomator2 Adapter)                │
│  ├── Appium适配器 (Appium Adapter) - 可选                     │
│  └── 设备管理 (Device Management)                              │
├─────────────────────────────────────────────────────────────────┤
│  📊 数据管理层 (Data Management Layer)                         │
│  ├── 测试数据 (Test Data)                                      │
│  ├── 配置管理 (Configuration)                                  │
│  ├── 环境管理 (Environment)                                    │
│  └── 数据工厂 (Data Factory)                                   │
├─────────────────────────────────────────────────────────────────┤
│  📈 报告监控层 (Reporting & Monitoring Layer)                  │
│  ├── Allure报告 (Allure Reports)                              │
│  ├── 性能监控 (Performance Monitoring)                         │
│  ├── 截图管理 (Screenshot Management)                          │
│  └── 日志管理 (Log Management)                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 核心模块设计

#### 1. Page Object层架构
```
framework/pages/
├── base/                           # 基础页面类
│   ├── base_page.py               # 基础页面抽象类
│   ├── base_component.py          # 基础组件抽象类
│   └── page_factory.py            # 页面工厂
├── components/                     # 通用组件
│   ├── navigation_bar.py          # 导航栏组件
│   ├── search_box.py              # 搜索框组件
│   ├── product_card.py            # 商品卡片组件
│   ├── form_component.py          # 表单组件
│   └── dialog_component.py        # 对话框组件
├── pages/                          # 具体页面对象
│   ├── login_page.py              # 登录页面
│   ├── home_page.py               # 首页
│   ├── product_page.py            # 商品页面
│   ├── cart_page.py               # 购物车页面
│   └── profile_page.py            # 个人中心页面
└── locators/                       # 元素定位器
    ├── login_locators.py          # 登录页面定位器
    ├── home_locators.py           # 首页定位器
    └── common_locators.py         # 通用定位器
```

#### 2. 操作封装层架构
```
framework/actions/
├── base/                           # 基础操作类
│   ├── base_action.py             # 基础操作抽象类
│   ├── action_result.py           # 操作结果类
│   └── action_chain.py            # 操作链
├── basic/                          # 基础操作
│   ├── click_action.py            # 点击操作
│   ├── input_action.py            # 输入操作
│   ├── swipe_action.py            # 滑动操作
│   ├── wait_action.py             # 等待操作
│   └── verify_action.py           # 验证操作
├── composite/                      # 复合操作
│   ├── login_action.py            # 登录操作
│   ├── search_action.py           # 搜索操作
│   ├── purchase_action.py         # 购买操作
│   └── navigation_action.py       # 导航操作
└── gestures/                       # 手势操作
    ├── pinch_action.py            # 缩放手势
    ├── drag_action.py             # 拖拽手势
    └── multi_touch_action.py      # 多点触控
```

#### 3. 智能体生成器优化
```
backend/app/agents/android/optimized/
├── page_object_generator_agent.py     # Page Object生成智能体
├── component_analyzer_agent.py        # 组件分析智能体
├── test_flow_optimizer_agent.py       # 测试流程优化智能体
└── code_quality_agent.py              # 代码质量检查智能体
```

## 🔄 核心业务流程优化

### 1. 智能Page Object生成流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant NLP as 自然语言解析
    participant CA as 组件分析智能体
    participant POG as Page Object生成智能体
    participant CQA as 代码质量智能体

    U->>NLP: 输入自然语言用例
    NLP->>CA: 发送界面分析请求
    CA->>CA: 识别页面组件和元素
    CA->>POG: 发送组件分析结果
    POG->>POG: 生成Page Object类
    POG->>CQA: 发送生成的代码
    CQA->>CQA: 代码质量检查和优化
    CQA->>U: 返回优化后的Page Object
```

### 2. 测试脚本生成优化流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant POG as Page Object生成器
    participant TFO as 测试流程优化器
    participant SG as 脚本生成器
    participant DM as 数据管理器

    U->>POG: 确认Page Object
    POG->>TFO: 发送页面对象信息
    TFO->>TFO: 分析测试流程
    TFO->>SG: 发送优化后的测试流程
    SG->>DM: 获取测试数据模板
    DM->>SG: 返回数据模板
    SG->>SG: 生成优化的测试脚本
    SG->>U: 返回完整测试项目
```

## 💻 核心代码实现

### 1. 基础Page Object框架

#### BasePage抽象类
```python
"""
基础页面抽象类
提供所有页面对象的通用功能和接口
"""
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union, Tuple
from loguru import logger
import allure
import uiautomator2 as u2

from framework.core.element_manager import ElementManager
from framework.core.smart_waiter import SmartWaiter
from framework.core.action_executor import ActionExecutor
from framework.utils.screenshot_manager import ScreenshotManager

class BasePage(ABC):
    """基础页面抽象类"""
    
    def __init__(self, driver: u2.Device, timeout: int = 10):
        """
        初始化基础页面
        
        Args:
            driver: uiautomator2设备实例
            timeout: 默认超时时间
        """
        self.driver = driver
        self.timeout = timeout
        self.element_manager = ElementManager(driver)
        self.smart_waiter = SmartWaiter(driver)
        self.action_executor = ActionExecutor(driver)
        self.screenshot_manager = ScreenshotManager(driver)
        
        # 页面标识符，子类必须实现
        self.page_identifier = self.get_page_identifier()
        
        logger.info(f"初始化页面: {self.__class__.__name__}")
    
    @abstractmethod
    def get_page_identifier(self) -> Dict[str, str]:
        """
        获取页面标识符，用于验证页面是否正确加载
        
        Returns:
            包含页面标识元素的字典
        """
        pass
    
    @abstractmethod
    def get_page_elements(self) -> Dict[str, Dict[str, str]]:
        """
        获取页面元素定位器
        
        Returns:
            页面元素定位器字典
        """
        pass
    
    def wait_for_page_load(self, timeout: Optional[int] = None) -> bool:
        """
        等待页面加载完成
        
        Args:
            timeout: 超时时间，默认使用实例超时时间
            
        Returns:
            页面是否成功加载
        """
        timeout = timeout or self.timeout
        
        with allure.step(f"等待页面加载: {self.__class__.__name__}"):
            try:
                # 等待页面标识元素出现
                for locator_type, locator_value in self.page_identifier.items():
                    if not self.smart_waiter.wait_for_element(
                        locator_type, locator_value, timeout
                    ):
                        logger.error(f"页面加载失败，标识元素未找到: {locator_type}={locator_value}")
                        return False
                
                # 等待页面稳定
                self.smart_waiter.wait_for_page_stable()
                
                logger.info(f"页面加载成功: {self.__class__.__name__}")
                return True
                
            except Exception as e:
                logger.error(f"页面加载异常: {str(e)}")
                return False
    
    def is_page_displayed(self) -> bool:
        """
        检查页面是否正确显示
        
        Returns:
            页面是否正确显示
        """
        try:
            for locator_type, locator_value in self.page_identifier.items():
                if not self.element_manager.is_element_present(locator_type, locator_value):
                    return False
            return True
        except Exception as e:
            logger.error(f"检查页面显示状态失败: {str(e)}")
            return False
    
    def get_element(self, element_name: str) -> Optional[u2.UiObject]:
        """
        获取页面元素
        
        Args:
            element_name: 元素名称
            
        Returns:
            UI元素对象
        """
        elements = self.get_page_elements()
        if element_name not in elements:
            raise ValueError(f"元素 '{element_name}' 未在页面 '{self.__class__.__name__}' 中定义")
        
        locator = elements[element_name]
        return self.element_manager.find_element(locator)
    
    def click_element(self, element_name: str, timeout: Optional[int] = None) -> bool:
        """
        点击页面元素
        
        Args:
            element_name: 元素名称
            timeout: 超时时间
            
        Returns:
            点击是否成功
        """
        with allure.step(f"点击元素: {element_name}"):
            try:
                element = self.get_element(element_name)
                return self.action_executor.click(element, timeout)
            except Exception as e:
                logger.error(f"点击元素失败: {element_name}, 错误: {str(e)}")
                self.screenshot_manager.capture_failure_screenshot(f"click_{element_name}_failed")
                return False
    
    def input_text(self, element_name: str, text: str, clear_first: bool = True) -> bool:
        """
        在元素中输入文本
        
        Args:
            element_name: 元素名称
            text: 要输入的文本
            clear_first: 是否先清空
            
        Returns:
            输入是否成功
        """
        with allure.step(f"输入文本到 {element_name}: {text}"):
            try:
                element = self.get_element(element_name)
                return self.action_executor.input_text(element, text, clear_first)
            except Exception as e:
                logger.error(f"输入文本失败: {element_name}, 错误: {str(e)}")
                self.screenshot_manager.capture_failure_screenshot(f"input_{element_name}_failed")
                return False
    
    def get_element_text(self, element_name: str) -> Optional[str]:
        """
        获取元素文本
        
        Args:
            element_name: 元素名称
            
        Returns:
            元素文本内容
        """
        try:
            element = self.get_element(element_name)
            return self.element_manager.get_element_text(element)
        except Exception as e:
            logger.error(f"获取元素文本失败: {element_name}, 错误: {str(e)}")
            return None
    
    def verify_element_present(self, element_name: str, timeout: Optional[int] = None) -> bool:
        """
        验证元素是否存在
        
        Args:
            element_name: 元素名称
            timeout: 超时时间
            
        Returns:
            元素是否存在
        """
        with allure.step(f"验证元素存在: {element_name}"):
            try:
                elements = self.get_page_elements()
                if element_name not in elements:
                    return False
                
                locator = elements[element_name]
                return self.smart_waiter.wait_for_element(
                    locator.get("type", "text"),
                    locator.get("value", ""),
                    timeout or self.timeout
                )
            except Exception as e:
                logger.error(f"验证元素存在失败: {element_name}, 错误: {str(e)}")
                return False
    
    def verify_text_present(self, expected_text: str, timeout: Optional[int] = None) -> bool:
        """
        验证页面包含指定文本
        
        Args:
            expected_text: 期望的文本
            timeout: 超时时间
            
        Returns:
            文本是否存在
        """
        with allure.step(f"验证文本存在: {expected_text}"):
            try:
                return self.smart_waiter.wait_for_text(expected_text, timeout or self.timeout)
            except Exception as e:
                logger.error(f"验证文本存在失败: {expected_text}, 错误: {str(e)}")
                return False
    
    def swipe_to_element(self, element_name: str, direction: str = "up", max_swipes: int = 5) -> bool:
        """
        滑动到指定元素
        
        Args:
            element_name: 元素名称
            direction: 滑动方向
            max_swipes: 最大滑动次数
            
        Returns:
            是否找到元素
        """
        with allure.step(f"滑动查找元素: {element_name}"):
            try:
                elements = self.get_page_elements()
                if element_name not in elements:
                    return False
                
                locator = elements[element_name]
                return self.action_executor.swipe_to_element(locator, direction, max_swipes)
            except Exception as e:
                logger.error(f"滑动查找元素失败: {element_name}, 错误: {str(e)}")
                return False
    
    def capture_page_screenshot(self, name: Optional[str] = None) -> str:
        """
        捕获页面截图
        
        Args:
            name: 截图名称
            
        Returns:
            截图文件路径
        """
        screenshot_name = name or f"{self.__class__.__name__}_{int(time.time())}"
        return self.screenshot_manager.capture_screenshot(screenshot_name)
    
    def get_page_source(self) -> str:
        """
        获取页面源码
        
        Returns:
            页面XML源码
        """
        try:
            return self.driver.dump_hierarchy()
        except Exception as e:
            logger.error(f"获取页面源码失败: {str(e)}")
            return ""
    
    def refresh_page(self) -> bool:
        """
        刷新页面
        
        Returns:
            刷新是否成功
        """
        with allure.step("刷新页面"):
            try:
                # Android应用通常通过下拉刷新
                self.action_executor.swipe("down", scale=0.8)
                time.sleep(2)
                return self.wait_for_page_load()
            except Exception as e:
                logger.error(f"刷新页面失败: {str(e)}")
                return False
    
    def go_back(self) -> bool:
        """
        返回上一页
        
        Returns:
            返回是否成功
        """
        with allure.step("返回上一页"):
            try:
                self.driver.press("back")
                time.sleep(1)
                return True
            except Exception as e:
                logger.error(f"返回上一页失败: {str(e)}")
                return False
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(driver={self.driver.serial})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return self.__str__()
```

### 2. 智能元素管理器

#### ElementManager类
```python
"""
智能元素管理器
提供元素查找、缓存、智能定位等功能
"""
import time
import hashlib
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass
from loguru import logger
import uiautomator2 as u2

@dataclass
class ElementCache:
    """元素缓存数据类"""
    element: u2.UiObject
    timestamp: float
    locator: Dict[str, str]
    page_hash: str

class ElementManager:
    """智能元素管理器"""
    
    def __init__(self, driver: u2.Device, cache_timeout: int = 30):
        """
        初始化元素管理器
        
        Args:
            driver: uiautomator2设备实例
            cache_timeout: 缓存超时时间（秒）
        """
        self.driver = driver
        self.cache_timeout = cache_timeout
        self.element_cache: Dict[str, ElementCache] = {}
        self.locator_strategies = {
            "text": self._find_by_text,
            "resource_id": self._find_by_resource_id,
            "description": self._find_by_description,
            "class_name": self._find_by_class_name,
            "xpath": self._find_by_xpath,
            "coordinates": self._find_by_coordinates,
            "smart": self._find_by_smart_strategy
        }
    
    def find_element(self, locator: Dict[str, str], use_cache: bool = True) -> Optional[u2.UiObject]:
        """
        查找元素
        
        Args:
            locator: 定位器字典，包含type和value
            use_cache: 是否使用缓存
            
        Returns:
            UI元素对象
        """
        try:
            locator_type = locator.get("type", "text")
            locator_value = locator.get("value", "")
            
            # 生成缓存键
            cache_key = self._generate_cache_key(locator)
            
            # 检查缓存
            if use_cache and self._is_cache_valid(cache_key):
                cached_element = self.element_cache[cache_key].element
                if cached_element.exists:
                    logger.debug(f"使用缓存元素: {locator}")
                    return cached_element
                else:
                    # 缓存的元素已失效，删除缓存
                    del self.element_cache[cache_key]
            
            # 查找元素
            strategy = self.locator_strategies.get(locator_type, self._find_by_text)
            element = strategy(locator_value, locator)
            
            # 缓存元素
            if element and element.exists and use_cache:
                self._cache_element(cache_key, element, locator)
            
            return element
            
        except Exception as e:
            logger.error(f"查找元素失败: {locator}, 错误: {str(e)}")
            return None
    
    def find_elements(self, locator: Dict[str, str]) -> List[u2.UiObject]:
        """
        查找多个元素
        
        Args:
            locator: 定位器字典
            
        Returns:
            UI元素对象列表
        """
        try:
            locator_type = locator.get("type", "text")
            locator_value = locator.get("value", "")
            
            if locator_type == "text":
                elements = self.driver(text=locator_value)
            elif locator_type == "resource_id":
                elements = self.driver(resourceId=locator_value)
            elif locator_type == "description":
                elements = self.driver(description=locator_value)
            elif locator_type == "class_name":
                elements = self.driver(className=locator_value)
            else:
                return []
            
            # 返回所有匹配的元素
            return [elements[i] for i in range(len(elements))]
            
        except Exception as e:
            logger.error(f"查找多个元素失败: {locator}, 错误: {str(e)}")
            return []
    
    def is_element_present(self, locator_type: str, locator_value: str, timeout: int = 5) -> bool:
        """
        检查元素是否存在
        
        Args:
            locator_type: 定位器类型
            locator_value: 定位器值
            timeout: 超时时间
            
        Returns:
            元素是否存在
        """
        try:
            locator = {"type": locator_type, "value": locator_value}
            element = self.find_element(locator, use_cache=False)
            
            if element:
                return element.wait(timeout=timeout)
            return False
            
        except Exception as e:
            logger.error(f"检查元素存在失败: {locator_type}={locator_value}, 错误: {str(e)}")
            return False
    
    def get_element_text(self, element: u2.UiObject) -> Optional[str]:
        """
        获取元素文本
        
        Args:
            element: UI元素对象
            
        Returns:
            元素文本内容
        """
        try:
            if element and element.exists:
                return element.get_text()
            return None
        except Exception as e:
            logger.error(f"获取元素文本失败: {str(e)}")
            return None
    
    def get_element_attribute(self, element: u2.UiObject, attribute: str) -> Optional[str]:
        """
        获取元素属性
        
        Args:
            element: UI元素对象
            attribute: 属性名称
            
        Returns:
            属性值
        """
        try:
            if element and element.exists:
                info = element.info
                return info.get(attribute)
            return None
        except Exception as e:
            logger.error(f"获取元素属性失败: {attribute}, 错误: {str(e)}")
            return None
    
    def clear_cache(self):
        """清空元素缓存"""
        self.element_cache.clear()
        logger.info("元素缓存已清空")
    
    def _find_by_text(self, value: str, locator: Dict[str, str]) -> Optional[u2.UiObject]:
        """通过文本查找元素"""
        try:
            # 支持精确匹配和包含匹配
            if locator.get("match_type") == "contains":
                return self.driver(textContains=value)
            elif locator.get("match_type") == "starts_with":
                return self.driver(textStartsWith=value)
            else:
                return self.driver(text=value)
        except Exception as e:
            logger.error(f"通过文本查找元素失败: {value}, 错误: {str(e)}")
            return None
    
    def _find_by_resource_id(self, value: str, locator: Dict[str, str]) -> Optional[u2.UiObject]:
        """通过resource-id查找元素"""
        try:
            return self.driver(resourceId=value)
        except Exception as e:
            logger.error(f"通过resource-id查找元素失败: {value}, 错误: {str(e)}")
            return None
    
    def _find_by_description(self, value: str, locator: Dict[str, str]) -> Optional[u2.UiObject]:
        """通过content-desc查找元素"""
        try:
            if locator.get("match_type") == "contains":
                return self.driver(descriptionContains=value)
            else:
                return self.driver(description=value)
        except Exception as e:
            logger.error(f"通过description查找元素失败: {value}, 错误: {str(e)}")
            return None
    
    def _find_by_class_name(self, value: str, locator: Dict[str, str]) -> Optional[u2.UiObject]:
        """通过class name查找元素"""
        try:
            return self.driver(className=value)
        except Exception as e:
            logger.error(f"通过class name查找元素失败: {value}, 错误: {str(e)}")
            return None
    
    def _find_by_xpath(self, value: str, locator: Dict[str, str]) -> Optional[u2.UiObject]:
        """通过XPath查找元素"""
        try:
            # uiautomator2不直接支持XPath，这里可以扩展实现
            logger.warning("uiautomator2不直接支持XPath定位")
            return None
        except Exception as e:
            logger.error(f"通过XPath查找元素失败: {value}, 错误: {str(e)}")
            return None
    
    def _find_by_coordinates(self, value: str, locator: Dict[str, str]) -> Optional[u2.UiObject]:
        """通过坐标查找元素"""
        try:
            # 解析坐标
            coords = value.split(",")
            if len(coords) == 2:
                x, y = int(coords[0]), int(coords[1])
                # 创建一个虚拟的点击对象
                return self.driver.click(x, y)
            return None
        except Exception as e:
            logger.error(f"通过坐标查找元素失败: {value}, 错误: {str(e)}")
            return None
    
    def _find_by_smart_strategy(self, value: str, locator: Dict[str, str]) -> Optional[u2.UiObject]:
        """智能查找策略"""
        try:
            # 尝试多种定位方式
            strategies = [
                ("text", value),
                ("resource_id", value),
                ("description", value)
            ]
            
            for strategy_type, strategy_value in strategies:
                element = self.locator_strategies[strategy_type](strategy_value, locator)
                if element and element.exists:
                    return element
            
            return None
        except Exception as e:
            logger.error(f"智能查找策略失败: {value}, 错误: {str(e)}")
            return None
    
    def _generate_cache_key(self, locator: Dict[str, str]) -> str:
        """生成缓存键"""
        locator_str = f"{locator.get('type', '')}:{locator.get('value', '')}"
        return hashlib.md5(locator_str.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.element_cache:
            return False
        
        cache_entry = self.element_cache[cache_key]
        current_time = time.time()
        
        # 检查时间是否过期
        if current_time - cache_entry.timestamp > self.cache_timeout:
            del self.element_cache[cache_key]
            return False
        
        # 检查页面是否发生变化
        current_page_hash = self._get_page_hash()
        if current_page_hash != cache_entry.page_hash:
            del self.element_cache[cache_key]
            return False
        
        return True
    
    def _cache_element(self, cache_key: str, element: u2.UiObject, locator: Dict[str, str]):
        """缓存元素"""
        try:
            page_hash = self._get_page_hash()
            cache_entry = ElementCache(
                element=element,
                timestamp=time.time(),
                locator=locator,
                page_hash=page_hash
            )
            self.element_cache[cache_key] = cache_entry
        except Exception as e:
            logger.error(f"缓存元素失败: {str(e)}")
    
    def _get_page_hash(self) -> str:
        """获取页面哈希值"""
        try:
            # 使用页面源码的哈希值作为页面标识
            page_source = self.driver.dump_hierarchy()
            return hashlib.md5(page_source.encode()).hexdigest()
        except Exception as e:
            logger.error(f"获取页面哈希失败: {str(e)}")
            return str(time.time())
```

### 3. Page Object生成智能体

#### PageObjectGeneratorAgent类
```python
"""
Page Object生成智能体
基于界面分析结果自动生成Page Object类代码
"""
import json
import re
from typing import Dict, List, Any, Optional
from autogen_core import message_handler, type_subscription, MessageContext
from loguru import logger

from app.core.messages.android import PageObjectGenerationRequest, PageObjectGenerationResult
from app.core.agents.base import BaseAgent
from app.core.types import TopicTypes, AgentTypes

@type_subscription(topic_type=TopicTypes.PAGE_OBJECT_GENERATION.value)
class PageObjectGeneratorAgent(BaseAgent):
    """Page Object生成智能体"""
    
    def __init__(self, model_client, **kwargs):
        super().__init__(
            name=AgentTypes.PAGE_OBJECT_GENERATOR.value,
            model_client=model_client,
            **kwargs
        )
        self.system_message = self._get_system_message()
    
    def _get_system_message(self) -> str:
        return """你是一个专业的Android Page Object模式代码生成专家，精通UI自动化测试最佳实践。

核心能力：
1. **Page Object设计**: 基于界面分析生成符合Page Object模式的Python类
2. **元素定位优化**: 选择最稳定和高效的元素定位策略
3. **代码质量保证**: 生成高质量、可维护、可扩展的代码
4. **最佳实践应用**: 遵循UI自动化测试最佳实践

设计原则：
- 单一职责：每个Page Object只负责一个页面
- 封装性：隐藏实现细节，提供简洁的接口
- 可维护性：元素定位与业务逻辑分离
- 可复用性：通用组件抽象和复用
- 可扩展性：支持新功能和页面的快速扩展

代码结构要求：
1. 继承BasePage基类
2. 定义页面标识符和元素定位器
3. 实现页面特有的业务操作方法
4. 包含完善的错误处理和日志记录
5. 添加详细的文档字符串和类型注解

请生成完整的Page Object类代码，确保代码质量和可维护性。"""

    @message_handler
    async def handle_generation_request(
        self, 
        message: PageObjectGenerationRequest, 
        ctx: MessageContext
    ) -> PageObjectGenerationResult:
        """处理Page Object生成请求"""
        try:
            logger.info(f"开始生成Page Object，页面: {message.page_name}")
            
            # 分析页面组件
            component_analysis = await self._analyze_page_components(message)
            
            # 生成Page Object类
            page_object_code = await self._generate_page_object_class(message, component_analysis)
            
            # 生成定位器文件
            locators_code = await self._generate_locators_file(message, component_analysis)
            
            # 生成测试基类
            test_base_code = await self._generate_test_base_class(message)
            
            # 生成项目文件
            project_files = self._generate_project_files(
                page_object_code, locators_code, test_base_code, message
            )
            
            logger.info(f"Page Object生成完成: {message.page_name}")
            
            return PageObjectGenerationResult(
                page_name=message.page_name,
                page_object_code=page_object_code,
                locators_code=locators_code,
                test_base_code=test_base_code,
                project_files=project_files,
                component_analysis=component_analysis
            )
            
        except Exception as e:
            logger.error(f"Page Object生成失败: {str(e)}")
            raise

    async def _analyze_page_components(self, message: PageObjectGenerationRequest) -> Dict[str, Any]:
        """分析页面组件"""
        analysis_prompt = f"""
分析以下Android页面的UI组件结构：

页面名称：{message.page_name}
页面类型：{message.page_type}

UI元素列表：
{json.dumps(message.ui_elements, ensure_ascii=False, indent=2)}

请分析并返回JSON格式的组件结构：
{{
  "page_identifier": {{
    "primary": {{"type": "text", "value": "主要标识元素"}},
    "secondary": {{"type": "resource_id", "value": "次要标识元素"}}
  }},
  "components": [
    {{
      "name": "组件名称",
      "type": "组件类型",
      "elements": [
        {{"name": "元素名", "locator": {{"type": "text", "value": "元素值"}}}}
      ],
      "actions": ["可执行的操作列表"]
    }}
  ],
  "common_elements": [
    {{"name": "通用元素名", "locator": {{"type": "resource_id", "value": "元素值"}}}}
  ],
  "business_flows": [
    {{
      "name": "业务流程名",
      "steps": ["步骤1", "步骤2"],
      "elements_involved": ["涉及的元素"]
    }}
  ]
}}
"""
        
        response = await self._call_text_model(analysis_prompt)
        
        try:
            # 提取JSON内容
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_content = response[json_start:json_end]
                return json.loads(json_content)
        except Exception as e:
            logger.error(f"解析组件分析结果失败: {str(e)}")
        
        # 返回默认结构
        return {
            "page_identifier": {"primary": {"type": "text", "value": message.page_name}},
            "components": [],
            "common_elements": [],
            "business_flows": []
        }

    async def _generate_page_object_class(
        self, 
        message: PageObjectGenerationRequest, 
        component_analysis: Dict[str, Any]
    ) -> str:
        """生成Page Object类代码"""
        generation_prompt = f"""
基于以下信息生成Android Page Object类代码：

页面名称：{message.page_name}
页面类型：{message.page_type}
组件分析：{json.dumps(component_analysis, ensure_ascii=False, indent=2)}

代码要求：
1. 继承BasePage基类
2. 实现get_page_identifier()和get_page_elements()方法
3. 为每个业务流程创建对应的方法
4. 包含完善的错误处理和日志记录
5. 添加详细的文档字符串和类型注解
6. 使用allure装饰器标记测试步骤

请生成完整的Python类代码：
"""
        
        response = await self._call_text_model(generation_prompt)
        
        # 提取Python代码
        code_match = re.search(r'```python\n(.*?)\n```', response, re.DOTALL)
        if code_match:
            return code_match.group(1)
        
        # 如果没有找到代码块，返回整个响应
        return response

    async def _generate_locators_file(
        self, 
        message: PageObjectGenerationRequest, 
        component_analysis: Dict[str, Any]
    ) -> str:
        """生成定位器文件"""
        locators_prompt = f"""
基于以下组件分析生成定位器文件：

页面名称：{message.page_name}
组件分析：{json.dumps(component_analysis, ensure_ascii=False, indent=2)}

生成包含所有元素定位器的Python文件，格式如下：

```python
\"\"\"
{message.page_name}页面元素定位器
\"\"\"
from typing import Dict

class {self._to_class_name(message.page_name)}Locators:
    \"\"\"页面元素定位器\"\"\"
    
    # 页面标识符
    PAGE_IDENTIFIER = {{
        "primary": {{"type": "text", "value": "标识元素"}},
        "secondary": {{"type": "resource_id", "value": "次要标识"}}
    }}
    
    # 页面元素
    ELEMENTS = {{
        "element_name": {{"type": "text", "value": "元素值"}},
        # 更多元素...
    }}
```

请生成完整的定位器文件代码。
"""
        
        response = await self._call_text_model(locators_prompt)
        
        # 提取Python代码
        code_match = re.search(r'```python\n(.*?)\n```', response, re.DOTALL)
        if code_match:
            return code_match.group(1)
        
        return response

    async def _generate_test_base_class(self, message: PageObjectGenerationRequest) -> str:
        """生成测试基类"""
        test_base_prompt = f"""
为{message.page_name}页面生成pytest测试基类：

要求：
1. 继承基础测试类
2. 包含页面对象的初始化
3. 提供通用的测试方法
4. 集成allure报告
5. 包含测试数据管理

请生成完整的测试基类代码。
"""
        
        response = await self._call_text_model(test_base_prompt)
        
        # 提取Python代码
        code_match = re.search(r'```python\n(.*?)\n```', response, re.DOTALL)
        if code_match:
            return code_match.group(1)
        
        return response

    def _generate_project_files(
        self, 
        page_object_code: str, 
        locators_code: str, 
        test_base_code: str,
        message: PageObjectGenerationRequest
    ) -> Dict[str, str]:
        """生成完整的项目文件"""
        page_name_snake = self._to_snake_case(message.page_name)
        page_name_class = self._to_class_name(message.page_name)
        
        return {
            f"pages/{page_name_snake}_page.py": page_object_code,
            f"locators/{page_name_snake}_locators.py": locators_code,
            f"tests/test_{page_name_snake}_base.py": test_base_code,
            "conftest.py": self._get_conftest(),
            "pytest.ini": self._get_pytest_ini(),
            "requirements.txt": self._get_requirements(),
            "README.md": self._get_readme(message.page_name),
            "__init__.py": "",
            "pages/__init__.py": "",
            "locators/__init__.py": "",
            "tests/__init__.py": ""
        }

    def _to_snake_case(self, name: str) -> str:
        """转换为蛇形命名"""
        # 移除特殊字符，转换为小写，用下划线连接
        name = re.sub(r'[^\w\s]', '', name)
        name = re.sub(r'\s+', '_', name.strip())
        return name.lower()

    def _to_class_name(self, name: str) -> str:
        """转换为类名格式"""
        # 移除特殊字符，每个单词首字母大写
        name = re.sub(r'[^\w\s]', '', name)
        words = name.split()
        return ''.join(word.capitalize() for word in words)

    def _get_conftest(self) -> str:
        """生成conftest.py"""
        return '''"""
pytest配置文件
"""
import pytest
import uiautomator2 as u2
import allure
from typing import Generator

from framework.core.device_manager import DeviceManager
from framework.core.test_data_manager import TestDataManager

@pytest.fixture(scope="session")
def device_manager() -> DeviceManager:
    """设备管理器夹具"""
    return DeviceManager()

@pytest.fixture(scope="session")
def device(device_manager) -> Generator[u2.Device, None, None]:
    """设备连接夹具"""
    device = device_manager.get_device()
    yield device
    device_manager.cleanup()

@pytest.fixture(scope="function")
def app_context(device):
    """应用上下文夹具"""
    device.screen_on()
    device.unlock()
    yield device
    device.press("home")

@pytest.fixture(scope="session")
def test_data() -> TestDataManager:
    """测试数据管理器"""
    return TestDataManager()

@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """测试报告钩子"""
    outcome = yield
    rep = outcome.get_result()
    
    if rep.when == "call" and rep.failed:
        if hasattr(item, "funcargs") and "device" in item.funcargs:
            device = item.funcargs["device"]
            screenshot = device.screenshot(format='raw')
            allure.attach(
                screenshot,
                name="失败截图",
                attachment_type=allure.attachment_type.PNG
            )
'''

    def _get_pytest_ini(self) -> str:
        """生成pytest.ini"""
        return '''[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --allure-dir=allure-results
    --clean-alluredir
    --tb=short
    -v
    --strict-markers
markers =
    smoke: 冒烟测试
    regression: 回归测试
    ui: UI测试
    android: Android测试
    page_object: Page Object测试
'''

    def _get_requirements(self) -> str:
        """生成requirements.txt"""
        return '''uiautomator2>=3.4.0
pytest>=7.0.0
allure-pytest>=2.12.0
pytest-html>=3.1.0
pytest-xdist>=3.0.0
pytest-rerunfailures>=11.0
Pillow>=9.0.0
loguru>=0.6.0
pydantic>=2.0.0
PyYAML>=6.0
'''

    def _get_readme(self, page_name: str) -> str:
        """生成README.md"""
        return f'''# {page_name} Page Object 自动化测试

## 项目结构

```
.
├── pages/              # Page Object类
├── locators/           # 元素定位器
├── tests/              # 测试用例
├── framework/          # 测试框架
├── data/               # 测试数据
├── conftest.py         # pytest配置
├── pytest.ini         # pytest设置
└── requirements.txt    # 依赖包
```

## 快速开始

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 连接Android设备并启用USB调试

3. 运行测试：
```bash
pytest tests/ -v --allure-dir=allure-results
```

4. 生成测试报告：
```bash
allure serve allure-results
```

## Page Object模式

本项目采用Page Object模式，具有以下优势：
- 元素定位与测试逻辑分离
- 提高代码可维护性和复用性
- 降低UI变更对测试的影响
- 提供清晰的测试结构

## 最佳实践

1. 每个页面对应一个Page Object类
2. 元素定位器统一管理
3. 业务操作封装为方法
4. 使用智能等待策略
5. 完善的错误处理和日志记录
'''
```

## 🔧 具体Page Object实现示例

### 1. 登录页面Page Object

#### LoginPage类实现
```python
"""
登录页面Page Object
演示完整的Page Object实现模式
"""
from typing import Dict, Optional, Tuple
import allure
from loguru import logger

from framework.pages.base.base_page import BasePage
from framework.locators.login_locators import LoginLocators
from framework.actions.composite.login_action import LoginAction

class LoginPage(BasePage):
    """登录页面Page Object"""

    def __init__(self, driver, timeout: int = 10):
        """
        初始化登录页面

        Args:
            driver: uiautomator2设备实例
            timeout: 默认超时时间
        """
        super().__init__(driver, timeout)
        self.locators = LoginLocators()
        self.login_action = LoginAction(driver)

    def get_page_identifier(self) -> Dict[str, str]:
        """获取登录页面标识符"""
        return self.locators.PAGE_IDENTIFIER

    def get_page_elements(self) -> Dict[str, Dict[str, str]]:
        """获取登录页面元素定位器"""
        return self.locators.ELEMENTS

    @allure.step("输入用户名")
    def input_username(self, username: str) -> bool:
        """
        输入用户名

        Args:
            username: 用户名

        Returns:
            输入是否成功
        """
        return self.input_text("username_input", username)

    @allure.step("输入密码")
    def input_password(self, password: str) -> bool:
        """
        输入密码

        Args:
            password: 密码

        Returns:
            输入是否成功
        """
        return self.input_text("password_input", password, clear_first=True)

    @allure.step("点击登录按钮")
    def click_login_button(self) -> bool:
        """
        点击登录按钮

        Returns:
            点击是否成功
        """
        return self.click_element("login_button")

    @allure.step("点击忘记密码链接")
    def click_forgot_password(self) -> bool:
        """
        点击忘记密码链接

        Returns:
            点击是否成功
        """
        return self.click_element("forgot_password_link")

    @allure.step("点击注册链接")
    def click_register_link(self) -> bool:
        """
        点击注册链接

        Returns:
            点击是否成功
        """
        return self.click_element("register_link")

    @allure.step("切换密码可见性")
    def toggle_password_visibility(self) -> bool:
        """
        切换密码可见性

        Returns:
            切换是否成功
        """
        return self.click_element("password_visibility_toggle")

    @allure.step("执行登录操作")
    def login(self, username: str, password: str) -> bool:
        """
        执行完整的登录操作

        Args:
            username: 用户名
            password: 密码

        Returns:
            登录是否成功
        """
        try:
            # 等待页面加载
            if not self.wait_for_page_load():
                logger.error("登录页面加载失败")
                return False

            # 使用复合操作执行登录
            return self.login_action.perform_login(username, password)

        except Exception as e:
            logger.error(f"登录操作失败: {str(e)}")
            self.capture_page_screenshot("login_failed")
            return False

    @allure.step("快速登录")
    def quick_login(self, user_type: str = "default") -> bool:
        """
        使用预设用户快速登录

        Args:
            user_type: 用户类型 (default, admin, test)

        Returns:
            登录是否成功
        """
        from framework.data.user_data import UserData

        user_data = UserData.get_user(user_type)
        if not user_data:
            logger.error(f"未找到用户类型: {user_type}")
            return False

        return self.login(user_data["username"], user_data["password"])

    @allure.step("验证登录错误信息")
    def verify_login_error(self, expected_error: str) -> bool:
        """
        验证登录错误信息

        Args:
            expected_error: 期望的错误信息

        Returns:
            验证是否通过
        """
        return self.verify_text_present(expected_error, timeout=5)

    @allure.step("验证用户名输入框状态")
    def verify_username_input_state(self, expected_state: str) -> bool:
        """
        验证用户名输入框状态

        Args:
            expected_state: 期望状态 (enabled, disabled, focused)

        Returns:
            验证是否通过
        """
        element = self.get_element("username_input")
        if not element:
            return False

        element_info = element.info

        if expected_state == "enabled":
            return element_info.get("enabled", False)
        elif expected_state == "disabled":
            return not element_info.get("enabled", True)
        elif expected_state == "focused":
            return element_info.get("focused", False)

        return False

    @allure.step("获取登录按钮文本")
    def get_login_button_text(self) -> Optional[str]:
        """
        获取登录按钮文本

        Returns:
            按钮文本内容
        """
        return self.get_element_text("login_button")

    @allure.step("检查记住密码选项")
    def is_remember_password_checked(self) -> bool:
        """
        检查记住密码选项是否选中

        Returns:
            是否选中
        """
        element = self.get_element("remember_password_checkbox")
        if element:
            return element.info.get("checked", False)
        return False

    @allure.step("切换记住密码选项")
    def toggle_remember_password(self) -> bool:
        """
        切换记住密码选项

        Returns:
            切换是否成功
        """
        return self.click_element("remember_password_checkbox")

    def clear_login_form(self) -> bool:
        """
        清空登录表单

        Returns:
            清空是否成功
        """
        with allure.step("清空登录表单"):
            try:
                # 清空用户名
                username_element = self.get_element("username_input")
                if username_element:
                    username_element.clear_text()

                # 清空密码
                password_element = self.get_element("password_input")
                if password_element:
                    password_element.clear_text()

                return True
            except Exception as e:
                logger.error(f"清空登录表单失败: {str(e)}")
                return False

    def get_form_validation_errors(self) -> List[str]:
        """
        获取表单验证错误信息

        Returns:
            错误信息列表
        """
        errors = []

        # 检查用户名错误
        username_error = self.get_element_text("username_error")
        if username_error:
            errors.append(f"用户名: {username_error}")

        # 检查密码错误
        password_error = self.get_element_text("password_error")
        if password_error:
            errors.append(f"密码: {password_error}")

        # 检查通用错误
        general_error = self.get_element_text("general_error")
        if general_error:
            errors.append(f"通用: {general_error}")

        return errors
```

#### LoginLocators定位器类
```python
"""
登录页面元素定位器
集中管理所有登录页面的元素定位信息
"""
from typing import Dict

class LoginLocators:
    """登录页面元素定位器"""

    # 页面标识符
    PAGE_IDENTIFIER = {
        "primary": {"type": "text", "value": "登录"},
        "secondary": {"type": "resource_id", "value": "login_container"}
    }

    # 页面元素定位器
    ELEMENTS = {
        # 输入框
        "username_input": {
            "type": "resource_id",
            "value": "et_username",
            "description": "用户名输入框",
            "fallback": {"type": "text", "value": "请输入用户名"}
        },
        "password_input": {
            "type": "resource_id",
            "value": "et_password",
            "description": "密码输入框",
            "fallback": {"type": "text", "value": "请输入密码"}
        },

        # 按钮
        "login_button": {
            "type": "resource_id",
            "value": "btn_login",
            "description": "登录按钮",
            "fallback": {"type": "text", "value": "登录"}
        },
        "forgot_password_link": {
            "type": "text",
            "value": "忘记密码？",
            "description": "忘记密码链接"
        },
        "register_link": {
            "type": "text",
            "value": "立即注册",
            "description": "注册链接"
        },

        # 控制元素
        "password_visibility_toggle": {
            "type": "resource_id",
            "value": "iv_password_toggle",
            "description": "密码可见性切换"
        },
        "remember_password_checkbox": {
            "type": "resource_id",
            "value": "cb_remember_password",
            "description": "记住密码复选框"
        },

        # 错误信息
        "username_error": {
            "type": "resource_id",
            "value": "tv_username_error",
            "description": "用户名错误提示"
        },
        "password_error": {
            "type": "resource_id",
            "value": "tv_password_error",
            "description": "密码错误提示"
        },
        "general_error": {
            "type": "resource_id",
            "value": "tv_login_error",
            "description": "通用错误提示"
        },

        # 加载状态
        "loading_indicator": {
            "type": "resource_id",
            "value": "pb_loading",
            "description": "加载指示器"
        }
    }

    # 第三方登录元素
    THIRD_PARTY_LOGIN = {
        "wechat_login": {
            "type": "resource_id",
            "value": "btn_wechat_login",
            "description": "微信登录按钮"
        },
        "qq_login": {
            "type": "resource_id",
            "value": "btn_qq_login",
            "description": "QQ登录按钮"
        },
        "phone_login": {
            "type": "text",
            "value": "手机号登录",
            "description": "手机号登录按钮"
        }
    }

    @classmethod
    def get_all_elements(cls) -> Dict[str, Dict[str, str]]:
        """获取所有元素定位器"""
        all_elements = {}
        all_elements.update(cls.ELEMENTS)
        all_elements.update(cls.THIRD_PARTY_LOGIN)
        return all_elements

    @classmethod
    def get_element_by_name(cls, element_name: str) -> Dict[str, str]:
        """根据名称获取元素定位器"""
        all_elements = cls.get_all_elements()
        return all_elements.get(element_name, {})

    @classmethod
    def validate_locators(cls) -> Dict[str, bool]:
        """验证定位器完整性"""
        validation_result = {}

        for element_name, locator in cls.get_all_elements().items():
            is_valid = (
                "type" in locator and
                "value" in locator and
                locator["type"] in ["text", "resource_id", "description", "class_name"]
            )
            validation_result[element_name] = is_valid

        return validation_result
```

### 2. 复合操作封装

#### LoginAction复合操作类
```python
"""
登录复合操作类
封装登录相关的复杂业务操作
"""
import time
from typing import Dict, Optional, Tuple
import allure
from loguru import logger
import uiautomator2 as u2

from framework.actions.base.base_action import BaseAction
from framework.core.smart_waiter import SmartWaiter
from framework.data.user_data import UserData

class LoginAction(BaseAction):
    """登录复合操作类"""

    def __init__(self, driver: u2.Device):
        """
        初始化登录操作

        Args:
            driver: uiautomator2设备实例
        """
        super().__init__(driver)
        self.smart_waiter = SmartWaiter(driver)

    @allure.step("执行标准登录流程")
    def perform_login(self, username: str, password: str, remember: bool = False) -> bool:
        """
        执行标准登录流程

        Args:
            username: 用户名
            password: 密码
            remember: 是否记住密码

        Returns:
            登录是否成功
        """
        try:
            # 步骤1: 清空现有输入
            self._clear_login_inputs()

            # 步骤2: 输入用户名
            if not self._input_username(username):
                return False

            # 步骤3: 输入密码
            if not self._input_password(password):
                return False

            # 步骤4: 设置记住密码选项
            if remember:
                self._set_remember_password(True)

            # 步骤5: 点击登录按钮
            if not self._click_login_button():
                return False

            # 步骤6: 等待登录结果
            return self._wait_for_login_result()

        except Exception as e:
            logger.error(f"登录流程执行失败: {str(e)}")
            return False

    @allure.step("快速登录")
    def quick_login(self, user_type: str = "default") -> bool:
        """
        使用预设用户数据快速登录

        Args:
            user_type: 用户类型

        Returns:
            登录是否成功
        """
        user_data = UserData.get_user(user_type)
        if not user_data:
            logger.error(f"未找到用户类型: {user_type}")
            return False

        return self.perform_login(
            user_data["username"],
            user_data["password"],
            user_data.get("remember", False)
        )

    @allure.step("批量登录测试")
    def batch_login_test(self, test_cases: List[Dict[str, Any]]) -> Dict[str, bool]:
        """
        批量登录测试

        Args:
            test_cases: 测试用例列表

        Returns:
            测试结果字典
        """
        results = {}

        for i, test_case in enumerate(test_cases):
            case_name = test_case.get("name", f"case_{i}")
            username = test_case.get("username", "")
            password = test_case.get("password", "")
            expected_result = test_case.get("expected", True)

            with allure.step(f"测试用例: {case_name}"):
                actual_result = self.perform_login(username, password)
                results[case_name] = (actual_result == expected_result)

                # 如果登录成功，需要退出登录准备下一个测试
                if actual_result:
                    self._logout_for_next_test()

        return results

    @allure.step("验证登录状态")
    def verify_login_success(self, timeout: int = 10) -> bool:
        """
        验证登录是否成功

        Args:
            timeout: 超时时间

        Returns:
            登录是否成功
        """
        # 等待登录成功的标识元素
        success_indicators = [
            {"type": "text", "value": "登录成功"},
            {"type": "text", "value": "欢迎"},
            {"type": "resource_id", "value": "main_container"},
            {"type": "text", "value": "首页"}
        ]

        for indicator in success_indicators:
            if self.smart_waiter.wait_for_element(
                indicator["type"], indicator["value"], timeout=timeout
            ):
                logger.info(f"登录成功，检测到标识: {indicator}")
                return True

        return False

    @allure.step("验证登录失败")
    def verify_login_failure(self, expected_error: Optional[str] = None) -> bool:
        """
        验证登录失败

        Args:
            expected_error: 期望的错误信息

        Returns:
            验证是否通过
        """
        # 等待错误信息出现
        error_indicators = [
            {"type": "text", "value": "用户名或密码错误"},
            {"type": "text", "value": "登录失败"},
            {"type": "resource_id", "value": "tv_login_error"}
        ]

        if expected_error:
            error_indicators.insert(0, {"type": "text", "value": expected_error})

        for indicator in error_indicators:
            if self.smart_waiter.wait_for_element(
                indicator["type"], indicator["value"], timeout=5
            ):
                logger.info(f"登录失败验证通过，检测到错误: {indicator}")
                return True

        return False

    def _clear_login_inputs(self):
        """清空登录输入框"""
        try:
            # 清空用户名
            username_input = self.driver(resourceId="et_username")
            if username_input.exists:
                username_input.clear_text()

            # 清空密码
            password_input = self.driver(resourceId="et_password")
            if password_input.exists:
                password_input.clear_text()

        except Exception as e:
            logger.warning(f"清空输入框失败: {str(e)}")

    def _input_username(self, username: str) -> bool:
        """输入用户名"""
        try:
            username_input = self.driver(resourceId="et_username")
            if not username_input.wait(timeout=5):
                logger.error("用户名输入框未找到")
                return False

            username_input.set_text(username)
            time.sleep(0.5)

            # 验证输入是否成功
            actual_text = username_input.get_text()
            if actual_text != username:
                logger.error(f"用户名输入验证失败，期望: {username}, 实际: {actual_text}")
                return False

            return True

        except Exception as e:
            logger.error(f"输入用户名失败: {str(e)}")
            return False

    def _input_password(self, password: str) -> bool:
        """输入密码"""
        try:
            password_input = self.driver(resourceId="et_password")
            if not password_input.wait(timeout=5):
                logger.error("密码输入框未找到")
                return False

            password_input.set_text(password)
            time.sleep(0.5)
            return True

        except Exception as e:
            logger.error(f"输入密码失败: {str(e)}")
            return False

    def _set_remember_password(self, remember: bool) -> bool:
        """设置记住密码选项"""
        try:
            checkbox = self.driver(resourceId="cb_remember_password")
            if not checkbox.exists:
                logger.warning("记住密码选项未找到")
                return True  # 不是必需的，返回True

            current_state = checkbox.info.get("checked", False)
            if current_state != remember:
                checkbox.click()
                time.sleep(0.3)

            return True

        except Exception as e:
            logger.warning(f"设置记住密码选项失败: {str(e)}")
            return True  # 不是关键操作

    def _click_login_button(self) -> bool:
        """点击登录按钮"""
        try:
            login_button = self.driver(resourceId="btn_login")
            if not login_button.wait(timeout=5):
                # 尝试通过文本查找
                login_button = self.driver(text="登录")
                if not login_button.wait(timeout=2):
                    logger.error("登录按钮未找到")
                    return False

            login_button.click()
            time.sleep(1)
            return True

        except Exception as e:
            logger.error(f"点击登录按钮失败: {str(e)}")
            return False

    def _wait_for_login_result(self, timeout: int = 15) -> bool:
        """等待登录结果"""
        try:
            # 等待加载指示器消失
            loading_indicator = self.driver(resourceId="pb_loading")
            if loading_indicator.exists:
                loading_indicator.wait_gone(timeout=timeout)

            # 检查登录是否成功
            return self.verify_login_success(timeout=5)

        except Exception as e:
            logger.error(f"等待登录结果失败: {str(e)}")
            return False

    def _logout_for_next_test(self):
        """为下一个测试退出登录"""
        try:
            # 这里实现退出登录的逻辑
            # 具体实现取决于应用的退出登录方式
            self.driver.press("back")  # 简单的返回操作
            time.sleep(1)
        except Exception as e:
            logger.warning(f"退出登录失败: {str(e)}")
```

### 3. 数据驱动测试

#### UserData测试数据管理
```python
"""
用户数据管理
提供测试用户数据和数据驱动测试支持
"""
import json
import yaml
from typing import Dict, List, Any, Optional
from pathlib import Path
from loguru import logger

class UserData:
    """用户数据管理类"""

    _users_cache = {}
    _data_file_path = Path("data/users.yaml")

    # 默认用户数据
    DEFAULT_USERS = {
        "default": {
            "username": "test_user",
            "password": "123456",
            "remember": False,
            "description": "默认测试用户"
        },
        "admin": {
            "username": "admin",
            "password": "admin123",
            "remember": True,
            "description": "管理员用户"
        },
        "invalid": {
            "username": "invalid_user",
            "password": "wrong_password",
            "remember": False,
            "description": "无效用户（用于负面测试）"
        },
        "empty": {
            "username": "",
            "password": "",
            "remember": False,
            "description": "空用户名密码"
        }
    }

    @classmethod
    def get_user(cls, user_type: str) -> Optional[Dict[str, Any]]:
        """
        获取指定类型的用户数据

        Args:
            user_type: 用户类型

        Returns:
            用户数据字典
        """
        try:
            # 首先尝试从缓存获取
            if cls._users_cache:
                return cls._users_cache.get(user_type)

            # 尝试从文件加载
            users_data = cls._load_users_from_file()
            if users_data:
                cls._users_cache = users_data
                return users_data.get(user_type)

            # 使用默认数据
            cls._users_cache = cls.DEFAULT_USERS
            return cls.DEFAULT_USERS.get(user_type)

        except Exception as e:
            logger.error(f"获取用户数据失败: {user_type}, 错误: {str(e)}")
            return cls.DEFAULT_USERS.get(user_type)

    @classmethod
    def get_all_users(cls) -> Dict[str, Dict[str, Any]]:
        """
        获取所有用户数据

        Returns:
            所有用户数据字典
        """
        if not cls._users_cache:
            cls._load_users_from_file()

        return cls._users_cache or cls.DEFAULT_USERS

    @classmethod
    def get_login_test_cases(cls) -> List[Dict[str, Any]]:
        """
        获取登录测试用例数据

        Returns:
            测试用例列表
        """
        test_cases = []

        # 正面测试用例
        positive_users = ["default", "admin"]
        for user_type in positive_users:
            user_data = cls.get_user(user_type)
            if user_data:
                test_cases.append({
                    "name": f"valid_login_{user_type}",
                    "username": user_data["username"],
                    "password": user_data["password"],
                    "expected": True,
                    "description": f"有效登录测试 - {user_data['description']}"
                })

        # 负面测试用例
        negative_cases = [
            {
                "name": "invalid_credentials",
                "username": "invalid_user",
                "password": "wrong_password",
                "expected": False,
                "description": "无效凭据测试"
            },
            {
                "name": "empty_username",
                "username": "",
                "password": "123456",
                "expected": False,
                "description": "空用户名测试"
            },
            {
                "name": "empty_password",
                "username": "test_user",
                "password": "",
                "expected": False,
                "description": "空密码测试"
            },
            {
                "name": "empty_both",
                "username": "",
                "password": "",
                "expected": False,
                "description": "用户名密码都为空"
            }
        ]

        test_cases.extend(negative_cases)
        return test_cases

    @classmethod
    def get_boundary_test_cases(cls) -> List[Dict[str, Any]]:
        """
        获取边界值测试用例

        Returns:
            边界值测试用例列表
        """
        return [
            {
                "name": "min_length_username",
                "username": "a",
                "password": "123456",
                "expected": False,
                "description": "最小长度用户名"
            },
            {
                "name": "max_length_username",
                "username": "a" * 50,
                "password": "123456",
                "expected": False,
                "description": "最大长度用户名"
            },
            {
                "name": "special_chars_username",
                "username": "test@#$%",
                "password": "123456",
                "expected": False,
                "description": "特殊字符用户名"
            },
            {
                "name": "chinese_username",
                "username": "测试用户",
                "password": "123456",
                "expected": False,
                "description": "中文用户名"
            }
        ]

    @classmethod
    def save_users_to_file(cls, users_data: Dict[str, Dict[str, Any]]):
        """
        保存用户数据到文件

        Args:
            users_data: 用户数据字典
        """
        try:
            # 确保目录存在
            cls._data_file_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存为YAML格式
            with open(cls._data_file_path, 'w', encoding='utf-8') as f:
                yaml.dump(users_data, f, default_flow_style=False, allow_unicode=True)

            # 更新缓存
            cls._users_cache = users_data
            logger.info(f"用户数据已保存到: {cls._data_file_path}")

        except Exception as e:
            logger.error(f"保存用户数据失败: {str(e)}")

    @classmethod
    def _load_users_from_file(cls) -> Optional[Dict[str, Dict[str, Any]]]:
        """从文件加载用户数据"""
        try:
            if not cls._data_file_path.exists():
                logger.info("用户数据文件不存在，使用默认数据")
                return None

            with open(cls._data_file_path, 'r', encoding='utf-8') as f:
                users_data = yaml.safe_load(f)

            logger.info(f"从文件加载用户数据: {cls._data_file_path}")
            return users_data

        except Exception as e:
            logger.error(f"加载用户数据文件失败: {str(e)}")
            return None

    @classmethod
    def validate_user_data(cls, user_data: Dict[str, Any]) -> bool:
        """
        验证用户数据格式

        Args:
            user_data: 用户数据

        Returns:
            数据是否有效
        """
        required_fields = ["username", "password"]

        for field in required_fields:
            if field not in user_data:
                logger.error(f"用户数据缺少必需字段: {field}")
                return False

        return True

    @classmethod
    def create_test_user(cls, username: str, password: str, **kwargs) -> Dict[str, Any]:
        """
        创建测试用户数据

        Args:
            username: 用户名
            password: 密码
            **kwargs: 其他用户属性

        Returns:
            用户数据字典
        """
        user_data = {
            "username": username,
            "password": password,
            "remember": kwargs.get("remember", False),
            "description": kwargs.get("description", f"测试用户 - {username}")
        }

        # 添加其他属性
        for key, value in kwargs.items():
            if key not in user_data:
                user_data[key] = value

        return user_data
```

---

## 🧪 测试用例实现示例

### 1. 基于Page Object的测试用例

#### LoginPageTest测试类
```python
"""
登录页面测试用例
演示基于Page Object模式的测试用例编写
"""
import pytest
import allure
from typing import Dict, Any
from loguru import logger

from framework.pages.login_page import LoginPage
from framework.pages.home_page import HomePage
from framework.data.user_data import UserData
from framework.utils.test_helpers import TestHelpers

@allure.epic("用户认证")
@allure.feature("登录功能")
@pytest.mark.android
@pytest.mark.login
class TestLoginPage:
    """登录页面测试类"""

    @pytest.fixture(autouse=True)
    def setup_test(self, app_context):
        """测试前置设置"""
        self.driver = app_context
        self.login_page = LoginPage(self.driver)
        self.home_page = HomePage(self.driver)
        self.test_helpers = TestHelpers(self.driver)

        # 确保在登录页面
        self.test_helpers.navigate_to_login_page()
        assert self.login_page.wait_for_page_load(), "登录页面加载失败"

    @allure.story("正常登录流程")
    @allure.title("使用有效凭据登录")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_valid_login(self):
        """测试有效凭据登录"""
        # 获取测试数据
        user_data = UserData.get_user("default")

        # 执行登录
        with allure.step("执行登录操作"):
            login_success = self.login_page.login(
                user_data["username"],
                user_data["password"]
            )
            assert login_success, "登录操作失败"

        # 验证登录成功
        with allure.step("验证登录成功"):
            assert self.home_page.wait_for_page_load(), "主页加载失败"
            assert self.home_page.is_user_logged_in(), "用户未成功登录"

            # 验证用户信息显示
            displayed_username = self.home_page.get_displayed_username()
            assert displayed_username == user_data["username"], \
                f"显示的用户名不正确，期望: {user_data['username']}, 实际: {displayed_username}"

    @allure.story("登录失败场景")
    @allure.title("使用无效凭据登录")
    @allure.severity(allure.severity_level.HIGH)
    @pytest.mark.regression
    def test_invalid_login(self):
        """测试无效凭据登录"""
        # 使用无效凭据
        invalid_username = "invalid_user"
        invalid_password = "wrong_password"

        # 执行登录
        with allure.step("使用无效凭据登录"):
            login_success = self.login_page.login(invalid_username, invalid_password)
            assert not login_success, "无效凭据不应该登录成功"

        # 验证错误信息
        with allure.step("验证错误信息显示"):
            assert self.login_page.verify_login_error("用户名或密码错误"), "错误信息未正确显示"
            assert self.login_page.is_page_displayed(), "应该仍在登录页面"

    @allure.story("数据驱动测试")
    @allure.title("批量登录测试")
    @pytest.mark.parametrize("test_case", UserData.get_login_test_cases())
    def test_login_with_various_credentials(self, test_case: Dict[str, Any]):
        """数据驱动的登录测试"""
        case_name = test_case["name"]
        username = test_case["username"]
        password = test_case["password"]
        expected_result = test_case["expected"]
        description = test_case["description"]

        with allure.step(f"执行测试用例: {case_name}"):
            allure.dynamic.description(description)

            # 清空表单
            self.login_page.clear_login_form()

            # 执行登录
            login_result = self.login_page.login(username, password)

            # 验证结果
            if expected_result:
                assert login_result, f"登录应该成功: {case_name}"
                assert self.home_page.wait_for_page_load(timeout=5), "主页应该加载成功"
                # 退出登录准备下一个测试
                self.test_helpers.logout()
                self.test_helpers.navigate_to_login_page()
            else:
                assert not login_result, f"登录应该失败: {case_name}"
                assert self.login_page.is_page_displayed(), "应该仍在登录页面"
```

### 2. 智能体生成的测试用例

#### 自然语言转测试用例示例
```python
"""
基于自然语言生成的测试用例
演示从自然语言描述到Page Object测试用例的转换
"""
import pytest
import allure
from framework.pages.shopping_cart_page import ShoppingCartPage
from framework.pages.product_page import ProductPage
from framework.pages.home_page import HomePage

@allure.epic("电商功能")
@allure.feature("购物车")
class TestShoppingCartFromNL:
    """基于自然语言生成的购物车测试"""

    @pytest.fixture(autouse=True)
    def setup_test(self, app_context):
        """测试前置设置"""
        self.driver = app_context
        self.home_page = HomePage(self.driver)
        self.product_page = ProductPage(self.driver)
        self.cart_page = ShoppingCartPage(self.driver)

        # 确保用户已登录
        self.home_page.ensure_user_logged_in()

    @allure.story("添加商品到购物车")
    @allure.title("搜索商品并添加到购物车")
    @allure.description("""
    原始自然语言用例：
    1. 打开购物应用
    2. 搜索商品"手机"
    3. 选择第一个商品
    4. 点击加入购物车
    5. 修改数量为2
    6. 点击立即购买
    7. 验证订单页面显示正确的商品和数量
    8. 验证总价计算正确
    """)
    @pytest.mark.smoke
    def test_add_product_to_cart_and_purchase(self):
        """搜索商品并添加到购物车进行购买"""

        # 步骤1: 确保在首页（已在setup中处理）
        with allure.step("确认在首页"):
            assert self.home_page.is_page_displayed(), "应该在首页"

        # 步骤2: 搜索商品"手机"
        with allure.step("搜索商品'手机'"):
            search_success = self.home_page.search_product("手机")
            assert search_success, "搜索操作失败"

        # 步骤3: 选择第一个商品
        with allure.step("选择第一个商品"):
            product_selected = self.home_page.select_first_product()
            assert product_selected, "选择商品失败"
            assert self.product_page.wait_for_page_load(), "商品详情页加载失败"

        # 步骤4: 点击加入购物车
        with allure.step("点击加入购物车"):
            add_to_cart_success = self.product_page.add_to_cart()
            assert add_to_cart_success, "加入购物车失败"

            # 验证加入购物车成功提示
            assert self.product_page.verify_add_to_cart_success(), "加入购物车成功提示未显示"

        # 步骤5: 修改数量为2
        with allure.step("修改商品数量为2"):
            quantity_updated = self.product_page.set_quantity(2)
            assert quantity_updated, "修改数量失败"

            # 验证数量显示正确
            current_quantity = self.product_page.get_current_quantity()
            assert current_quantity == 2, f"数量显示错误，期望: 2, 实际: {current_quantity}"

        # 步骤6: 点击立即购买
        with allure.step("点击立即购买"):
            purchase_clicked = self.product_page.click_buy_now()
            assert purchase_clicked, "点击立即购买失败"

            # 等待跳转到购物车或订单页面
            assert self.cart_page.wait_for_page_load(), "购物车页面加载失败"

        # 步骤7: 验证订单页面显示正确的商品和数量
        with allure.step("验证订单信息"):
            # 验证商品名称包含"手机"
            product_name = self.cart_page.get_product_name()
            assert "手机" in product_name, f"商品名称错误: {product_name}"

            # 验证数量为2
            order_quantity = self.cart_page.get_product_quantity()
            assert order_quantity == 2, f"订单数量错误，期望: 2, 实际: {order_quantity}"

        # 步骤8: 验证总价计算正确
        with allure.step("验证总价计算"):
            unit_price = self.cart_page.get_unit_price()
            total_price = self.cart_page.get_total_price()
            expected_total = unit_price * 2

            # 允许小的浮点数误差
            price_diff = abs(total_price - expected_total)
            assert price_diff < 0.01, f"总价计算错误，期望: {expected_total}, 实际: {total_price}"
```

## 📊 最佳实践和优化建议

### 1. Page Object设计原则

#### 设计原则清单
- **单一职责**: 每个Page Object只负责一个页面或组件
- **封装性**: 隐藏实现细节，提供简洁的业务接口
- **可维护性**: 元素定位与业务逻辑分离
- **可复用性**: 通用组件抽象和复用
- **可扩展性**: 支持新功能和页面的快速扩展

#### 命名规范
```python
# Page Object类命名
class LoginPage(BasePage):  # 页面名 + Page
class ProductListPage(BasePage):
class ShoppingCartPage(BasePage):

# 方法命名
def click_login_button(self):  # 动作 + 元素名
def input_username(self, username):  # 动作 + 元素名
def verify_error_message(self, message):  # verify + 验证内容
def get_product_count(self):  # get + 获取内容

# 元素定位器命名
LOGIN_BUTTON = {"type": "resource_id", "value": "btn_login"}
USERNAME_INPUT = {"type": "resource_id", "value": "et_username"}
ERROR_MESSAGE = {"type": "resource_id", "value": "tv_error"}
```

### 2. 测试数据管理最佳实践

#### 数据分层管理
```python
# 环境配置数据
class EnvironmentConfig:
    DEV = {
        "base_url": "https://dev.example.com",
        "app_package": "com.example.dev"
    }
    TEST = {
        "base_url": "https://test.example.com",
        "app_package": "com.example.test"
    }
    PROD = {
        "base_url": "https://prod.example.com",
        "app_package": "com.example.prod"
    }

# 测试数据工厂
class TestDataFactory:
    @staticmethod
    def create_user(user_type: str = "default"):
        """创建测试用户数据"""
        return UserData.get_user(user_type)

    @staticmethod
    def create_product(product_type: str = "default"):
        """创建测试商品数据"""
        return ProductData.get_product(product_type)
```

### 3. 错误处理和重试策略

#### 智能重试机制
```python
def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    logger.warning(f"操作失败，第{attempt + 1}次重试: {str(e)}")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator

# 使用示例
@retry_on_failure(max_retries=3, delay=1.0)
def click_element_with_retry(self, element_name: str):
    """带重试的点击操作"""
    return self.click_element(element_name)
```

### 4. 性能优化策略

#### 元素缓存优化
```python
class OptimizedElementManager(ElementManager):
    """优化的元素管理器"""

    def __init__(self, driver, cache_size: int = 100):
        super().__init__(driver)
        self.cache_size = cache_size
        self.access_count = {}

    def find_element_optimized(self, locator: Dict[str, str]):
        """优化的元素查找"""
        cache_key = self._generate_cache_key(locator)

        # LRU缓存策略
        if len(self.element_cache) >= self.cache_size:
            self._evict_least_used()

        # 更新访问计数
        self.access_count[cache_key] = self.access_count.get(cache_key, 0) + 1

        return self.find_element(locator)

    def _evict_least_used(self):
        """淘汰最少使用的缓存项"""
        if self.access_count:
            least_used_key = min(self.access_count, key=self.access_count.get)
            if least_used_key in self.element_cache:
                del self.element_cache[least_used_key]
            del self.access_count[least_used_key]
```

### 5. 持续集成优化

#### 并行测试执行
```python
# pytest.ini配置
[tool:pytest]
addopts =
    --allure-dir=allure-results
    --tb=short
    -v
    -n auto  # 自动并行执行
    --dist=loadscope  # 按作用域分发
    --maxfail=5  # 最多失败5个就停止

# 并行测试标记
@pytest.mark.parallel
class TestLoginParallel:
    """支持并行执行的登录测试"""

    @pytest.mark.thread_safe
    def test_login_user1(self):
        """线程安全的登录测试1"""
        pass

    @pytest.mark.thread_safe
    def test_login_user2(self):
        """线程安全的登录测试2"""
        pass
```

---

*本优化方案基于Page Object模式和UI自动化测试最佳实践，通过分层架构、智能元素管理、组件化设计、复合操作封装、数据驱动测试、完整的测试用例示例和最佳实践指南，显著提升了Android自动化测试的可维护性、可复用性和稳定性。完整的实现示例展示了如何将理论转化为实际可用的代码框架，为团队提供了一套完整的Android自动化测试解决方案。*
