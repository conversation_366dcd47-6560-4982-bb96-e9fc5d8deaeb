# 基于自然语言用例转Android自动化脚本技术落地方案

## 📋 方案概述

基于现有UI自动化测试系统架构，集成uiautomator2、pytest、allure等技术栈，实现从自然语言测试用例到Android自动化脚本的智能转换。本方案充分复用现有的多模态AI分析和智能体协作框架，扩展支持Android平台的自动化测试能力。

### 🎯 核心目标
- **自然语言理解**: 智能解析自然语言测试用例，提取测试意图和操作步骤
- **智能脚本生成**: 基于uiautomator2框架生成高质量的Python自动化脚本
- **测试框架集成**: 集成pytest测试框架和allure报告系统
- **无缝架构融合**: 复用现有智能体架构和AI模型能力

### 🔧 技术栈选择
- **Android自动化**: uiautomator2 (Python wrapper for Android UiAutomator)
- **测试框架**: pytest (灵活的Python测试框架)
- **测试报告**: allure (美观的测试报告系统)
- **AI模型**: 复用现有DeepSeek、UI-TARS、Qwen-VL模型
- **智能体框架**: 基于现有AutoGen智能体架构

## 🏗️ 技术架构设计

### 整体架构扩展

```
┌─────────────────────────────────────────────────────────────────┐
│           基于自然语言的Android自动化测试系统架构                │
├─────────────────────────────────────────────────────────────────┤
│  🌐 前端层 (React + TypeScript)                                │
│  ├── 自然语言用例编辑器 (NL Test Case Editor) ⭐新增             │
│  ├── Android设备管理 (Device Management)                       │
│  ├── 脚本生成预览 (Script Preview)                             │
│  ├── 测试执行监控 (Test Execution Monitor)                     │
│  └── Allure报告展示 (Allure Report Viewer)                    │
├─────────────────────────────────────────────────────────────────┤
│  🔌 API网关层 (FastAPI)                                        │
│  ├── 自然语言解析接口 (/android/nl-parse) ⭐新增               │
│  ├── 脚本生成接口 (/android/script-generate) ⭐新增            │
│  ├── pytest执行接口 (/android/pytest-run) ⭐新增              │
│  └── allure报告接口 (/android/allure-report) ⭐新增            │
├─────────────────────────────────────────────────────────────────┤
│  🔧 业务服务层                                                 │
│  ├── 自然语言解析服务 (NLParsingService) ⭐新增                │
│  ├── Android脚本生成服务 (AndroidScriptService) ⭐新增         │
│  ├── pytest执行服务 (PytestExecutionService) ⭐新增            │
│  └── Allure报告服务 (AllureReportService) ⭐新增               │
├─────────────────────────────────────────────────────────────────┤
│  🤖 智能体层 (AutoGen Framework)                               │
│  ├── 自然语言解析智能体 (NLParserAgent) ⭐新增                 │
│  ├── Android脚本生成智能体 (AndroidScriptGeneratorAgent) ⭐新增│
│  ├── pytest脚本优化智能体 (PytestOptimizerAgent) ⭐新增        │
│  └── 测试执行智能体 (TestExecutionAgent) ⭐新增                │
├─────────────────────────────────────────────────────────────────┤
│  💾 数据存储层                                                 │
│  ├── MySQL - 自然语言用例、生成脚本、执行记录                  │
│  ├── 文件系统 - pytest脚本文件、allure报告文件                │
│  └── Redis - 执行状态缓存、任务队列                            │
├─────────────────────────────────────────────────────────────────┤
│  🧠 AI模型层                                                   │
│  ├── DeepSeek-Chat (自然语言理解和脚本生成)                    │
│  ├── UI-TARS (Android界面元素识别)                             │
│  └── Qwen-VL-Max (多模态分析和验证)                            │
├─────────────────────────────────────────────────────────────────┤
│  🔌 集成层                                                     │
│  ├── uiautomator2 (Android设备控制) ⭐新增                     │
│  ├── pytest (测试框架) ⭐新增                                  │
│  ├── allure (测试报告) ⭐新增                                  │
│  └── ADB (Android Debug Bridge)                               │
└─────────────────────────────────────────────────────────────────┘
```

### 核心模块设计

#### 1. 自然语言解析模块
```
backend/app/agents/android/nl_parser_agent.py      # 自然语言解析智能体
backend/app/services/android/nl_parsing_service.py # 自然语言解析服务
backend/app/core/nl_parser/                        # 自然语言处理核心
├── intent_extractor.py                            # 意图提取器
├── action_parser.py                               # 操作解析器
├── element_mapper.py                              # 元素映射器
└── test_case_builder.py                           # 测试用例构建器
```

#### 2. Android脚本生成模块
```
backend/app/agents/android/script_generator_agent.py    # 脚本生成智能体
backend/app/services/android/script_generation_service.py # 脚本生成服务
backend/app/core/script_generator/                      # 脚本生成核心
├── uiautomator2_generator.py                           # uiautomator2脚本生成器
├── pytest_wrapper.py                                   # pytest包装器
├── allure_decorator.py                                 # allure装饰器
└── template_engine.py                                  # 模板引擎
```

#### 3. 测试执行模块
```
backend/app/agents/android/test_executor_agent.py       # 测试执行智能体
backend/app/services/android/pytest_execution_service.py # pytest执行服务
backend/app/core/test_execution/                        # 测试执行核心
├── pytest_runner.py                                    # pytest运行器
├── device_manager.py                                   # 设备管理器
├── execution_monitor.py                                # 执行监控器
└── result_collector.py                                 # 结果收集器
```

## 🔄 核心业务流程

### 1. 自然语言用例解析流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关
    participant NLP as 自然语言解析服务
    participant NLA as 自然语言解析智能体
    participant AI as AI模型

    U->>F: 输入自然语言测试用例
    F->>A: POST /android/nl-parse
    A->>NLP: 创建解析会话
    NLP->>NLA: 发送解析请求
    
    NLA->>AI: 调用DeepSeek进行语言理解
    AI->>NLA: 返回意图和操作序列
    NLA->>NLP: 返回结构化测试步骤
    
    NLP->>A: 返回解析结果
    A->>F: 返回结构化用例
    F->>U: 显示解析后的测试步骤
```

### 2. Android脚本生成流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关
    participant SGS as 脚本生成服务
    participant SGA as 脚本生成智能体
    participant AI as AI模型

    U->>F: 确认生成脚本
    F->>A: POST /android/script-generate
    A->>SGS: 创建生成会话
    SGS->>SGA: 发送脚本生成请求
    
    SGA->>AI: 调用DeepSeek生成uiautomator2脚本
    AI->>SGA: 返回Python脚本代码
    SGA->>SGS: 返回完整pytest脚本
    
    SGS->>A: 返回生成结果
    A->>F: 返回脚本内容
    F->>U: 显示生成的脚本
```

### 3. pytest执行和allure报告流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关
    participant PES as pytest执行服务
    participant TEA as 测试执行智能体
    participant pytest as pytest框架
    participant allure as allure报告

    U->>F: 执行测试脚本
    F->>A: POST /android/pytest-run
    A->>PES: 创建执行会话
    PES->>TEA: 发送执行请求
    
    TEA->>pytest: 启动pytest执行
    pytest->>pytest: 运行uiautomator2脚本
    pytest->>allure: 生成allure数据
    allure->>TEA: 返回测试报告
    
    TEA->>PES: 更新执行状态
    PES->>A: 返回执行结果
    A->>F: SSE推送执行状态
    F->>U: 显示测试结果和报告
```

## 💾 数据库设计

### 自然语言用例相关表

#### 1. 自然语言测试用例表
```sql
CREATE TABLE nl_test_cases (
    id VARCHAR(36) PRIMARY KEY,
    project_id VARCHAR(36) NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    nl_content LONGTEXT NOT NULL,
    parsed_steps JSON,
    target_app_package VARCHAR(200),
    device_requirements JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id)
);
```

#### 2. 生成的脚本表
```sql
CREATE TABLE generated_android_scripts (
    id VARCHAR(36) PRIMARY KEY,
    nl_case_id VARCHAR(36) NOT NULL,
    script_type ENUM('uiautomator2', 'pytest') DEFAULT 'pytest',
    script_content LONGTEXT NOT NULL,
    file_path VARCHAR(500),
    dependencies JSON,
    pytest_markers JSON,
    allure_labels JSON,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (nl_case_id) REFERENCES nl_test_cases(id)
);
```

#### 3. pytest执行记录表
```sql
CREATE TABLE pytest_executions (
    id VARCHAR(36) PRIMARY KEY,
    script_id VARCHAR(36) NOT NULL,
    device_id VARCHAR(100),
    execution_status ENUM('pending', 'running', 'passed', 'failed', 'skipped') DEFAULT 'pending',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP NULL,
    duration INT DEFAULT 0,
    pytest_result JSON,
    allure_report_path VARCHAR(500),
    error_message TEXT,
    screenshots_path VARCHAR(500),
    FOREIGN KEY (script_id) REFERENCES generated_android_scripts(id)
);
```

## 🤖 智能体系统实现

### 1. 自然语言解析智能体

```python
"""
自然语言解析智能体
负责将自然语言测试用例转换为结构化的测试步骤
"""
import json
from typing import Dict, List, Any, Optional
from autogen_core import message_handler, type_subscription, MessageContext
from loguru import logger

from app.core.messages.android import NLParsingRequest, NLParsingResult
from app.core.agents.base import BaseAgent
from app.core.types import TopicTypes, AgentTypes

@type_subscription(topic_type=TopicTypes.NL_PARSING.value)
class NLParserAgent(BaseAgent):
    """自然语言解析智能体"""
    
    def __init__(self, model_client, **kwargs):
        super().__init__(
            name=AgentTypes.NL_PARSER.value,
            model_client=model_client,
            **kwargs
        )
        self.system_message = self._get_system_message()
    
    def _get_system_message(self) -> str:
        return """你是一个专业的Android测试用例分析专家，专门将自然语言测试用例转换为结构化的测试步骤。

核心能力：
1. **意图识别**: 准确理解测试用例的测试目标和验证点
2. **操作解析**: 将自然语言操作转换为具体的Android UI操作
3. **元素映射**: 识别UI元素的定位方式和属性
4. **流程构建**: 构建完整的测试执行流程

支持的操作类型：
- 点击操作: 点击按钮、链接、图标等
- 输入操作: 在输入框中输入文本
- 滑动操作: 上下左右滑动、滚动列表
- 验证操作: 检查文本、元素存在性、状态等
- 等待操作: 等待元素出现、页面加载等
- 导航操作: 返回、前进、打开应用等

输出格式要求：
返回JSON格式的结构化测试步骤，包含：
- test_intent: 测试意图描述
- test_steps: 测试步骤列表
- verification_points: 验证点列表
- preconditions: 前置条件
- expected_results: 预期结果

示例输出：
{
  "test_intent": "验证用户登录功能",
  "preconditions": ["应用已安装", "网络连接正常"],
  "test_steps": [
    {
      "step_number": 1,
      "action": "click",
      "target": "登录按钮",
      "locator_type": "text",
      "locator_value": "登录",
      "description": "点击登录按钮"
    },
    {
      "step_number": 2,
      "action": "input",
      "target": "用户名输入框",
      "locator_type": "resource_id",
      "locator_value": "username",
      "input_value": "test_user",
      "description": "输入用户名"
    }
  ],
  "verification_points": [
    {
      "step_number": 3,
      "action": "verify_text",
      "target": "欢迎信息",
      "expected_value": "欢迎回来",
      "description": "验证登录成功提示"
    }
  ],
  "expected_results": ["成功登录到主页面", "显示用户信息"]
}

请严格按照JSON格式输出，确保结构完整且可解析。"""

    @message_handler
    async def handle_parsing_request(
        self, 
        message: NLParsingRequest, 
        ctx: MessageContext
    ) -> NLParsingResult:
        """处理自然语言解析请求"""
        try:
            logger.info(f"开始解析自然语言用例，会话ID: {message.session_id}")
            
            # 构建解析提示词
            parsing_prompt = self._build_parsing_prompt(message)
            
            # 调用AI模型进行解析
            response = await self._call_text_model(parsing_prompt)
            
            # 解析AI响应
            parsed_result = self._parse_nl_response(response)
            
            logger.info(f"自然语言解析完成，生成 {len(parsed_result.test_steps)} 个测试步骤")
            
            return parsed_result
            
        except Exception as e:
            logger.error(f"自然语言解析失败: {str(e)}")
            raise

    def _build_parsing_prompt(self, message: NLParsingRequest) -> str:
        """构建解析提示词"""
        prompt = f"""
请解析以下自然语言测试用例，转换为结构化的测试步骤：

测试用例名称：{message.case_name}
测试用例描述：
{message.nl_content}

目标应用：{message.target_app or '未指定'}
设备要求：{message.device_requirements or '无特殊要求'}

请分析测试用例的意图，识别所有操作步骤和验证点，并按照指定的JSON格式输出结构化结果。
"""
        
        if message.additional_context:
            prompt += f"\n额外上下文信息：{message.additional_context}"
            
        return prompt

    def _parse_nl_response(self, response: str) -> NLParsingResult:
        """解析AI响应为结构化结果"""
        try:
            # 提取JSON内容
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start == -1 or json_end == 0:
                raise ValueError("响应中未找到有效的JSON格式")
            
            json_content = response[json_start:json_end]
            parsed_data = json.loads(json_content)
            
            return NLParsingResult(
                test_intent=parsed_data.get('test_intent', ''),
                test_steps=parsed_data.get('test_steps', []),
                verification_points=parsed_data.get('verification_points', []),
                preconditions=parsed_data.get('preconditions', []),
                expected_results=parsed_data.get('expected_results', []),
                confidence_score=self._calculate_parsing_confidence(parsed_data)
            )
            
        except Exception as e:
            logger.error(f"解析AI响应失败: {str(e)}")
            # 返回默认结果
            return NLParsingResult(
                test_intent="解析失败",
                test_steps=[],
                verification_points=[],
                preconditions=[],
                expected_results=[],
                confidence_score=0.0
            )

    def _calculate_parsing_confidence(self, parsed_data: Dict) -> float:
        """计算解析置信度"""
        score = 0.5  # 基础分数
        
        # 检查必要字段
        if parsed_data.get('test_intent'):
            score += 0.1
        if parsed_data.get('test_steps'):
            score += 0.2
        if parsed_data.get('verification_points'):
            score += 0.1
        
        # 检查步骤完整性
        steps = parsed_data.get('test_steps', [])
        if steps:
            complete_steps = sum(1 for step in steps 
                               if all(key in step for key in ['action', 'target', 'description']))
            score += (complete_steps / len(steps)) * 0.1
        
        return min(score, 0.95)
```

### 2. Android脚本生成智能体

```python
"""
Android脚本生成智能体
基于解析后的测试步骤生成uiautomator2 + pytest脚本
"""
import json
from typing import Dict, List, Any
from autogen_core import message_handler, type_subscription, MessageContext

from app.core.messages.android import AndroidScriptGenerationRequest, AndroidGeneratedScript
from app.core.agents.base import BaseAgent

@type_subscription(topic_type=TopicTypes.ANDROID_SCRIPT_GENERATION.value)
class AndroidScriptGeneratorAgent(BaseAgent):
    """Android脚本生成智能体"""
    
    def __init__(self, model_client, **kwargs):
        super().__init__(
            name=AgentTypes.ANDROID_SCRIPT_GENERATOR.value,
            model_client=model_client,
            **kwargs
        )
        self.system_message = self._get_system_message()
    
    def _get_system_message(self) -> str:
        return """你是一个专业的Android自动化测试脚本生成专家，精通uiautomator2和pytest框架。

核心能力：
1. **uiautomator2脚本生成**: 基于测试步骤生成高质量的uiautomator2 Python代码
2. **pytest框架集成**: 生成符合pytest规范的测试类和方法
3. **allure报告集成**: 添加allure装饰器和标签
4. **最佳实践应用**: 遵循Android自动化测试最佳实践

技术要求：
- 使用uiautomator2库进行Android设备控制
- 使用pytest框架组织测试代码
- 集成allure报告装饰器
- 包含完善的异常处理和重试机制
- 添加详细的注释和文档

脚本结构要求：
1. 导入必要的库和模块
2. 定义测试类和方法
3. 实现设备连接和初始化
4. 按步骤实现测试操作
5. 添加验证和断言
6. 包含清理和资源释放

代码质量要求：
- 代码结构清晰，易于维护
- 变量命名规范，符合Python PEP8
- 包含适当的等待和超时处理
- 添加错误处理和日志记录
- 支持参数化和数据驱动

请生成完整可执行的Python测试脚本。"""

    @message_handler
    async def handle_script_generation(
        self, 
        message: AndroidScriptGenerationRequest, 
        ctx: MessageContext
    ) -> AndroidGeneratedScript:
        """处理Android脚本生成请求"""
        try:
            logger.info(f"开始生成Android脚本，会话ID: {message.session_id}")
            
            # 构建脚本生成提示词
            generation_prompt = self._build_generation_prompt(message)
            
            # 调用AI模型生成脚本
            script_content = await self._generate_script(generation_prompt)
            
            # 生成完整的项目文件
            project_files = self._generate_project_files(script_content, message)
            
            logger.info("Android脚本生成完成")
            
            return AndroidGeneratedScript(
                format="uiautomator2-pytest",
                content=script_content,
                project_files=project_files,
                estimated_duration=self._estimate_duration(message.parsed_steps),
                framework="pytest",
                dependencies=self._get_dependencies()
            )
            
        except Exception as e:
            logger.error(f"Android脚本生成失败: {str(e)}")
            raise

    def _build_generation_prompt(self, message: AndroidScriptGenerationRequest) -> str:
        """构建脚本生成提示词"""
        return f"""
基于以下解析后的测试步骤，生成uiautomator2 + pytest自动化测试脚本：

测试用例名称：{message.case_name}
测试意图：{message.test_intent}

测试步骤：
{json.dumps(message.test_steps, ensure_ascii=False, indent=2)}

验证点：
{json.dumps(message.verification_points, ensure_ascii=False, indent=2)}

前置条件：
{message.preconditions}

预期结果：
{message.expected_results}

目标应用：{message.target_app or 'com.example.app'}
设备要求：{message.device_requirements or '无特殊要求'}

脚本要求：
1. 使用uiautomator2库连接和控制Android设备
2. 使用pytest框架组织测试代码
3. 集成allure报告装饰器和标签
4. 包含完整的设备初始化和清理
5. 实现所有测试步骤和验证点
6. 添加适当的等待和错误处理
7. 包含详细的注释说明

请生成完整的Python测试脚本，确保代码可直接执行。
"""

    def _generate_project_files(self, script_content: str, message: AndroidScriptGenerationRequest) -> Dict[str, str]:
        """生成完整的项目文件"""
        return {
            "test_script.py": script_content,
            "conftest.py": self._get_conftest(),
            "pytest.ini": self._get_pytest_ini(),
            "requirements.txt": self._get_requirements(),
            "README.md": self._get_readme(message.case_name),
            "allure_environment.properties": self._get_allure_environment()
        }

    def _get_conftest(self) -> str:
        """生成pytest配置文件"""
        return '''"""
pytest配置文件
提供测试夹具和全局配置
"""
import pytest
import uiautomator2 as u2
import allure
from typing import Generator

@pytest.fixture(scope="session")
def device() -> Generator[u2.Device, None, None]:
    """设备连接夹具"""
    # 连接设备
    d = u2.connect()
    
    # 设置隐式等待
    d.implicitly_wait(10.0)
    
    # 设置操作延迟
    d.settings['operation_delay'] = (0.2, 0.5)
    
    yield d
    
    # 清理资源
    d.app_stop_all(excludes=['com.android.systemui'])

@pytest.fixture(scope="function")
def app_context(device):
    """应用上下文夹具"""
    # 测试前准备
    device.screen_on()
    device.unlock()
    
    yield device
    
    # 测试后清理
    device.press("home")

@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """测试报告钩子，用于截图"""
    outcome = yield
    rep = outcome.get_result()
    
    if rep.when == "call" and rep.failed:
        # 测试失败时截图
        if hasattr(item, "funcargs") and "device" in item.funcargs:
            device = item.funcargs["device"]
            screenshot = device.screenshot(format='raw')
            allure.attach(
                screenshot,
                name="失败截图",
                attachment_type=allure.attachment_type.PNG
            )
'''

    def _get_pytest_ini(self) -> str:
        """生成pytest配置文件"""
        return '''[tool:pytest]
testpaths = .
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --allure-dir=allure-results
    --clean-alluredir
    --tb=short
    -v
markers =
    smoke: 冒烟测试
    regression: 回归测试
    ui: UI测试
    android: Android测试
'''

    def _get_requirements(self) -> str:
        """生成依赖文件"""
        return '''uiautomator2>=3.4.0
pytest>=7.0.0
allure-pytest>=2.12.0
pytest-html>=3.1.0
pytest-xdist>=3.0.0
Pillow>=9.0.0
loguru>=0.6.0
'''

    def _get_dependencies(self) -> List[str]:
        """获取依赖列表"""
        return [
            "uiautomator2>=3.4.0",
            "pytest>=7.0.0", 
            "allure-pytest>=2.12.0",
            "pytest-html>=3.1.0",
            "Pillow>=9.0.0",
            "loguru>=0.6.0"
        ]
```

## 📊 前端实现

### 自然语言用例编辑器

```typescript
/**
 * 自然语言测试用例编辑器
 * 支持用例编写、解析预览、脚本生成
 */
import React, { useState } from 'react';
import { 
  Card, 
  Input, 
  Button, 
  Steps, 
  message, 
  Spin,
  Row,
  Col,
  Typography,
  Divider,
  Tag,
  List
} from 'antd';
import { 
  EditOutlined, 
  CodeOutlined, 
  PlayCircleOutlined,
  CheckCircleOutlined 
} from '@ant-design/icons';
import { androidApi } from '@/services/androidApi';

const { TextArea } = Input;
const { Title, Paragraph, Text } = Typography;

interface ParsedStep {
  step_number: number;
  action: string;
  target: string;
  description: string;
  locator_type?: string;
  locator_value?: string;
  input_value?: string;
}

interface ParsedResult {
  test_intent: string;
  test_steps: ParsedStep[];
  verification_points: ParsedStep[];
  preconditions: string[];
  expected_results: string[];
  confidence_score: number;
}

const NLTestCaseEditor: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [caseName, setCaseName] = useState('');
  const [nlContent, setNlContent] = useState('');
  const [targetApp, setTargetApp] = useState('');
  const [parsedResult, setParsedResult] = useState<ParsedResult | null>(null);
  const [generatedScript, setGeneratedScript] = useState('');

  // 步骤配置
  const steps = [
    {
      title: '编写用例',
      description: '使用自然语言描述测试用例',
    },
    {
      title: '解析用例',
      description: 'AI解析生成结构化步骤',
    },
    {
      title: '生成脚本',
      description: '生成uiautomator2测试脚本',
    },
    {
      title: '执行测试',
      description: '运行pytest测试脚本',
    },
  ];

  // 解析自然语言用例
  const parseNLCase = async () => {
    if (!caseName || !nlContent) {
      message.error('请填写用例名称和内容');
      return;
    }

    setLoading(true);
    try {
      const response = await androidApi.parseNLCase({
        session_id: `session_${Date.now()}`,
        case_name: caseName,
        nl_content: nlContent,
        target_app: targetApp,
        device_requirements: {}
      });
      
      if (response.success) {
        setParsedResult(response.parsed_result);
        setCurrentStep(1);
        message.success('用例解析完成');
      }
    } catch (error) {
      message.error('用例解析失败');
    } finally {
      setLoading(false);
    }
  };

  // 生成测试脚本
  const generateScript = async () => {
    if (!parsedResult) {
      message.error('请先解析用例');
      return;
    }

    setLoading(true);
    try {
      const response = await androidApi.generateScript({
        session_id: `session_${Date.now()}`,
        case_name: caseName,
        test_intent: parsedResult.test_intent,
        test_steps: parsedResult.test_steps,
        verification_points: parsedResult.verification_points,
        preconditions: parsedResult.preconditions,
        expected_results: parsedResult.expected_results,
        target_app: targetApp
      });
      
      if (response.success) {
        setGeneratedScript(response.generated_script.content);
        setCurrentStep(2);
        message.success('脚本生成完成');
      }
    } catch (error) {
      message.error('脚本生成失败');
    } finally {
      setLoading(false);
    }
  };

  // 执行测试脚本
  const executeScript = async () => {
    if (!generatedScript) {
      message.error('请先生成脚本');
      return;
    }

    setLoading(true);
    try {
      const response = await androidApi.executePytest({
        execution_id: `exec_${Date.now()}`,
        script_content: generatedScript,
        case_name: caseName,
        target_app: targetApp
      });
      
      if (response.success) {
        setCurrentStep(3);
        message.success('测试脚本开始执行');
      }
    } catch (error) {
      message.error('脚本执行失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Card title="自然语言测试用例编辑器">
        <Steps current={currentStep} items={steps} style={{ marginBottom: 24 }} />
        
        <Spin spinning={loading}>
          {/* 步骤1: 编写用例 */}
          {currentStep === 0 && (
            <Card title="编写自然语言测试用例">
              <Row gutter={16}>
                <Col span={24}>
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>用例名称：</Text>
                    <Input
                      placeholder="请输入测试用例名称"
                      value={caseName}
                      onChange={(e) => setCaseName(e.target.value)}
                      style={{ marginTop: 8 }}
                    />
                  </div>
                  
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>目标应用包名：</Text>
                    <Input
                      placeholder="例如：com.example.app"
                      value={targetApp}
                      onChange={(e) => setTargetApp(e.target.value)}
                      style={{ marginTop: 8 }}
                    />
                  </div>
                  
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>测试用例描述：</Text>
                    <TextArea
                      placeholder="请用自然语言描述测试用例，例如：
1. 打开应用
2. 点击登录按钮
3. 输入用户名和密码
4. 点击登录
5. 验证登录成功"
                      value={nlContent}
                      onChange={(e) => setNlContent(e.target.value)}
                      rows={8}
                      style={{ marginTop: 8 }}
                    />
                  </div>
                  
                  <Button 
                    type="primary" 
                    icon={<EditOutlined />}
                    onClick={parseNLCase}
                    disabled={!caseName || !nlContent}
                  >
                    解析用例
                  </Button>
                </Col>
              </Row>
            </Card>
          )}

          {/* 步骤2: 解析结果 */}
          {currentStep === 1 && parsedResult && (
            <Card title="用例解析结果">
              <Row gutter={16}>
                <Col span={12}>
                  <Title level={5}>测试意图</Title>
                  <Paragraph>{parsedResult.test_intent}</Paragraph>
                  
                  <Title level={5}>前置条件</Title>
                  <List
                    size="small"
                    dataSource={parsedResult.preconditions}
                    renderItem={item => <List.Item>• {item}</List.Item>}
                  />
                  
                  <Title level={5}>
                    置信度: 
                    <Tag color={parsedResult.confidence_score > 0.8 ? 'green' : 'orange'}>
                      {(parsedResult.confidence_score * 100).toFixed(1)}%
                    </Tag>
                  </Title>
                </Col>
                
                <Col span={12}>
                  <Title level={5}>测试步骤</Title>
                  <List
                    size="small"
                    dataSource={parsedResult.test_steps}
                    renderItem={step => (
                      <List.Item>
                        <Text strong>{step.step_number}.</Text> {step.description}
                        <br />
                        <Text type="secondary">
                          操作: {step.action} | 目标: {step.target}
                        </Text>
                      </List.Item>
                    )}
                  />
                  
                  <Title level={5}>验证点</Title>
                  <List
                    size="small"
                    dataSource={parsedResult.verification_points}
                    renderItem={point => (
                      <List.Item>
                        <CheckCircleOutlined style={{ color: 'green', marginRight: 8 }} />
                        {point.description}
                      </List.Item>
                    )}
                  />
                </Col>
              </Row>
              
              <Divider />
              
              <Button 
                type="primary" 
                icon={<CodeOutlined />}
                onClick={generateScript}
              >
                生成测试脚本
              </Button>
            </Card>
          )}

          {/* 步骤3: 生成的脚本 */}
          {currentStep === 2 && generatedScript && (
            <Card title="生成的测试脚本">
              <div style={{ marginBottom: 16 }}>
                <Title level={5}>uiautomator2 + pytest 脚本</Title>
                <TextArea 
                  value={generatedScript} 
                  rows={15} 
                  readOnly 
                  style={{ fontFamily: 'monospace', fontSize: '12px' }}
                />
              </div>
              
              <Button 
                type="primary" 
                icon={<PlayCircleOutlined />}
                onClick={executeScript}
              >
                执行测试脚本
              </Button>
            </Card>
          )}

          {/* 步骤4: 执行结果 */}
          {currentStep === 3 && (
            <Card title="测试执行">
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <CheckCircleOutlined 
                  style={{ fontSize: '48px', color: 'green', marginBottom: '16px' }} 
                />
                <Title level={4}>测试脚本已开始执行</Title>
                <Paragraph>
                  请前往测试执行监控页面查看实时执行状态和allure测试报告
                </Paragraph>
              </div>
            </Card>
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default NLTestCaseEditor;
```

## 🔧 技术实现细节

### 1. uiautomator2脚本模板

```python
"""
uiautomator2 + pytest 脚本模板
自动生成的Android自动化测试脚本
"""
import pytest
import uiautomator2 as u2
import allure
import time
from loguru import logger

@allure.epic("Android自动化测试")
@allure.feature("{{feature_name}}")
@allure.story("{{story_name}}")
@pytest.mark.android
@pytest.mark.ui
class Test{{class_name}}:
    """{{test_description}}"""
    
    @allure.title("{{test_title}}")
    @allure.description("{{test_description}}")
    @allure.severity(allure.severity_level.{{severity}})
    def test_{{method_name}}(self, app_context):
        """
        {{test_intent}}
        
        前置条件：
        {{preconditions}}
        
        测试步骤：
        {{test_steps_description}}
        
        预期结果：
        {{expected_results}}
        """
        device = app_context
        
        try:
            # 启动目标应用
            with allure.step("启动应用 {{target_app}}"):
                device.app_start("{{target_app}}", stop=True)
                device.wait_activity("{{main_activity}}", timeout=10)
                logger.info("应用启动成功")
            
            {{generated_test_steps}}
            
            {{generated_verifications}}
            
        except Exception as e:
            logger.error(f"测试执行失败: {str(e)}")
            # 失败时截图
            screenshot = device.screenshot(format='raw')
            allure.attach(
                screenshot,
                name="失败截图",
                attachment_type=allure.attachment_type.PNG
            )
            raise
        
        finally:
            # 清理资源
            with allure.step("清理测试环境"):
                device.press("home")
                logger.info("测试环境清理完成")

    def _wait_for_element(self, device, locator_type, locator_value, timeout=10):
        """等待元素出现"""
        if locator_type == "text":
            return device(text=locator_value).wait(timeout=timeout)
        elif locator_type == "resource_id":
            return device(resourceId=locator_value).wait(timeout=timeout)
        elif locator_type == "description":
            return device(description=locator_value).wait(timeout=timeout)
        elif locator_type == "class_name":
            return device(className=locator_value).wait(timeout=timeout)
        else:
            raise ValueError(f"不支持的定位类型: {locator_type}")

    def _click_element(self, device, locator_type, locator_value, description=""):
        """点击元素"""
        with allure.step(f"点击元素: {description}"):
            if locator_type == "text":
                device(text=locator_value).click()
            elif locator_type == "resource_id":
                device(resourceId=locator_value).click()
            elif locator_type == "description":
                device(description=locator_value).click()
            elif locator_type == "class_name":
                device(className=locator_value).click()
            else:
                raise ValueError(f"不支持的定位类型: {locator_type}")
            
            logger.info(f"点击元素成功: {description}")
            time.sleep(0.5)  # 等待UI响应

    def _input_text(self, device, locator_type, locator_value, text, description=""):
        """输入文本"""
        with allure.step(f"输入文本: {description}"):
            if locator_type == "text":
                element = device(text=locator_value)
            elif locator_type == "resource_id":
                element = device(resourceId=locator_value)
            elif locator_type == "description":
                element = device(description=locator_value)
            elif locator_type == "class_name":
                element = device(className=locator_value)
            else:
                raise ValueError(f"不支持的定位类型: {locator_type}")
            
            element.clear_text()
            element.set_text(text)
            logger.info(f"输入文本成功: {description} = {text}")
            time.sleep(0.3)

    def _verify_text_exists(self, device, text, description=""):
        """验证文本存在"""
        with allure.step(f"验证文本存在: {description}"):
            exists = device(text=text).exists
            assert exists, f"文本不存在: {text}"
            logger.info(f"文本验证成功: {text}")

    def _verify_element_exists(self, device, locator_type, locator_value, description=""):
        """验证元素存在"""
        with allure.step(f"验证元素存在: {description}"):
            if locator_type == "text":
                exists = device(text=locator_value).exists
            elif locator_type == "resource_id":
                exists = device(resourceId=locator_value).exists
            elif locator_type == "description":
                exists = device(description=locator_value).exists
            elif locator_type == "class_name":
                exists = device(className=locator_value).exists
            else:
                raise ValueError(f"不支持的定位类型: {locator_type}")
            
            assert exists, f"元素不存在: {locator_value}"
            logger.info(f"元素验证成功: {description}")
```

### 2. pytest执行服务

```python
"""
pytest执行服务
负责执行生成的uiautomator2测试脚本
"""
import os
import subprocess
import asyncio
import json
from typing import Dict, Any, Optional
from pathlib import Path
from loguru import logger

from app.core.config import get_settings

class PytestExecutionService:
    """pytest执行服务"""
    
    def __init__(self):
        self.settings = get_settings()
        self.execution_dir = Path("test_executions")
        self.execution_dir.mkdir(exist_ok=True)
    
    async def execute_script(
        self, 
        execution_id: str,
        script_content: str,
        case_name: str,
        target_app: str,
        device_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """执行pytest脚本"""
        try:
            logger.info(f"开始执行pytest脚本: {execution_id}")
            
            # 创建执行目录
            exec_dir = self.execution_dir / execution_id
            exec_dir.mkdir(exist_ok=True)
            
            # 写入脚本文件
            script_file = exec_dir / "test_script.py"
            script_file.write_text(script_content, encoding='utf-8')
            
            # 生成配置文件
            await self._generate_config_files(exec_dir, case_name, target_app)
            
            # 执行pytest
            result = await self._run_pytest(exec_dir, device_id)
            
            # 生成allure报告
            allure_report_path = await self._generate_allure_report(exec_dir)
            
            return {
                "execution_id": execution_id,
                "status": "completed",
                "result": result,
                "allure_report_path": allure_report_path,
                "execution_dir": str(exec_dir)
            }
            
        except Exception as e:
            logger.error(f"pytest执行失败: {execution_id}, 错误: {str(e)}")
            return {
                "execution_id": execution_id,
                "status": "failed",
                "error": str(e)
            }
    
    async def _generate_config_files(self, exec_dir: Path, case_name: str, target_app: str):
        """生成配置文件"""
        # conftest.py
        conftest_content = '''
import pytest
import uiautomator2 as u2
import allure

@pytest.fixture(scope="session")
def device():
    d = u2.connect()
    d.implicitly_wait(10.0)
    d.settings['operation_delay'] = (0.2, 0.5)
    yield d
    d.app_stop_all(excludes=['com.android.systemui'])

@pytest.fixture(scope="function")
def app_context(device):
    device.screen_on()
    device.unlock()
    yield device
    device.press("home")
'''
        (exec_dir / "conftest.py").write_text(conftest_content)
        
        # pytest.ini
        pytest_ini_content = f'''
[tool:pytest]
testpaths = .
addopts = 
    --allure-dir=allure-results
    --clean-alluredir
    --tb=short
    -v
    --html=report.html
    --self-contained-html
markers =
    android: Android测试
    ui: UI测试
'''
        (exec_dir / "pytest.ini").write_text(pytest_ini_content)
        
        # allure环境配置
        allure_env_content = f'''
测试用例={case_name}
目标应用={target_app}
测试框架=uiautomator2 + pytest
执行时间={datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
'''
        allure_dir = exec_dir / "allure-results"
        allure_dir.mkdir(exist_ok=True)
        (allure_dir / "environment.properties").write_text(allure_env_content)
    
    async def _run_pytest(self, exec_dir: Path, device_id: Optional[str] = None) -> Dict[str, Any]:
        """运行pytest"""
        cmd = [
            "python", "-m", "pytest",
            "test_script.py",
            "--allure-dir=allure-results",
            "--tb=short",
            "-v",
            "--html=report.html",
            "--self-contained-html"
        ]
        
        env = os.environ.copy()
        if device_id:
            env["ANDROID_SERIAL"] = device_id
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            cwd=exec_dir,
            env=env,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        return {
            "return_code": process.returncode,
            "stdout": stdout.decode('utf-8', errors='ignore'),
            "stderr": stderr.decode('utf-8', errors='ignore'),
            "success": process.returncode == 0
        }
    
    async def _generate_allure_report(self, exec_dir: Path) -> str:
        """生成allure报告"""
        try:
            allure_results_dir = exec_dir / "allure-results"
            allure_report_dir = exec_dir / "allure-report"
            
            if not allure_results_dir.exists():
                logger.warning("allure结果目录不存在")
                return ""
            
            cmd = [
                "allure", "generate",
                str(allure_results_dir),
                "-o", str(allure_report_dir),
                "--clean"
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await process.communicate()
            
            if process.returncode == 0:
                return str(allure_report_dir / "index.html")
            else:
                logger.error("allure报告生成失败")
                return ""
                
        except Exception as e:
            logger.error(f"生成allure报告失败: {str(e)}")
            return ""

# 创建全局服务实例
pytest_execution_service = PytestExecutionService()
```

## 🚀 API接口实现

### 1. 自然语言解析API

```python
"""
自然语言解析API端点
提供自然语言测试用例解析功能
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any
from loguru import logger

from app.core.messages.android import NLParsingRequest, NLParsingResult
from app.services.android.nl_parsing_service import nl_parsing_service

router = APIRouter(prefix="/android/nl", tags=["自然语言解析"])

@router.post("/parse")
async def parse_nl_case(
    request: NLParsingRequest,
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """解析自然语言测试用例"""
    try:
        logger.info(f"开始解析自然语言用例: {request.case_name}")

        # 调用解析服务
        result = await nl_parsing_service.parse_nl_case(request)

        # 后台保存解析结果
        background_tasks.add_task(
            nl_parsing_service.save_parsed_case,
            request, result
        )

        return {
            "success": True,
            "session_id": request.session_id,
            "parsed_result": result,
            "message": "自然语言用例解析完成"
        }

    except Exception as e:
        logger.error(f"自然语言解析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/cases/{case_id}")
async def get_parsed_case(case_id: str) -> Dict[str, Any]:
    """获取已解析的测试用例"""
    try:
        case_data = await nl_parsing_service.get_parsed_case(case_id)

        if not case_data:
            raise HTTPException(status_code=404, detail="测试用例不存在")

        return {
            "success": True,
            "case_data": case_data
        }

    except Exception as e:
        logger.error(f"获取测试用例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/enhance")
async def enhance_parsed_case(
    case_id: str,
    enhancement_type: str = "detail"
) -> Dict[str, Any]:
    """增强解析结果"""
    try:
        enhanced_result = await nl_parsing_service.enhance_parsed_case(
            case_id, enhancement_type
        )

        return {
            "success": True,
            "enhanced_result": enhanced_result,
            "message": "用例增强完成"
        }

    except Exception as e:
        logger.error(f"用例增强失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
```

### 2. 脚本生成API

```python
"""
Android脚本生成API端点
提供uiautomator2 + pytest脚本生成功能
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any, List
from loguru import logger

from app.core.messages.android import AndroidScriptGenerationRequest, AndroidGeneratedScript
from app.services.android.script_generation_service import script_generation_service

router = APIRouter(prefix="/android/script", tags=["脚本生成"])

@router.post("/generate")
async def generate_android_script(
    request: AndroidScriptGenerationRequest,
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """生成Android自动化脚本"""
    try:
        logger.info(f"开始生成Android脚本: {request.case_name}")

        # 调用脚本生成服务
        generated_script = await script_generation_service.generate_script(request)

        # 后台保存生成的脚本
        background_tasks.add_task(
            script_generation_service.save_generated_script,
            request, generated_script
        )

        return {
            "success": True,
            "session_id": request.session_id,
            "generated_script": generated_script,
            "message": "Android脚本生成完成"
        }

    except Exception as e:
        logger.error(f"Android脚本生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/templates")
async def get_script_templates() -> Dict[str, Any]:
    """获取脚本模板列表"""
    try:
        templates = await script_generation_service.get_available_templates()

        return {
            "success": True,
            "templates": templates
        }

    except Exception as e:
        logger.error(f"获取脚本模板失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/optimize")
async def optimize_script(
    script_id: str,
    optimization_type: str = "performance"
) -> Dict[str, Any]:
    """优化生成的脚本"""
    try:
        optimized_script = await script_generation_service.optimize_script(
            script_id, optimization_type
        )

        return {
            "success": True,
            "optimized_script": optimized_script,
            "message": "脚本优化完成"
        }

    except Exception as e:
        logger.error(f"脚本优化失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/validate")
async def validate_script(
    script_content: str,
    target_app: str
) -> Dict[str, Any]:
    """验证脚本语法和逻辑"""
    try:
        validation_result = await script_generation_service.validate_script(
            script_content, target_app
        )

        return {
            "success": True,
            "validation_result": validation_result,
            "message": "脚本验证完成"
        }

    except Exception as e:
        logger.error(f"脚本验证失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
```

### 3. pytest执行API

```python
"""
pytest执行API端点
提供测试脚本执行和监控功能
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any, Optional
from loguru import logger

from app.core.messages.android import PytestExecutionRequest
from app.services.android.pytest_execution_service import pytest_execution_service

router = APIRouter(prefix="/android/pytest", tags=["pytest执行"])

@router.post("/execute")
async def execute_pytest_script(
    request: PytestExecutionRequest,
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """执行pytest脚本"""
    try:
        logger.info(f"开始执行pytest脚本: {request.execution_id}")

        # 后台执行脚本
        background_tasks.add_task(
            pytest_execution_service.execute_script,
            request.execution_id,
            request.script_content,
            request.case_name,
            request.target_app,
            request.device_id
        )

        return {
            "success": True,
            "execution_id": request.execution_id,
            "status": "started",
            "message": "pytest脚本执行已启动"
        }

    except Exception as e:
        logger.error(f"pytest脚本执行失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/execution/{execution_id}/status")
async def get_execution_status(execution_id: str) -> Dict[str, Any]:
    """获取执行状态"""
    try:
        status = await pytest_execution_service.get_execution_status(execution_id)

        return {
            "success": True,
            "execution_id": execution_id,
            "status": status
        }

    except Exception as e:
        logger.error(f"获取执行状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/execution/{execution_id}/logs")
async def get_execution_logs(execution_id: str) -> Dict[str, Any]:
    """获取执行日志"""
    try:
        logs = await pytest_execution_service.get_execution_logs(execution_id)

        return {
            "success": True,
            "execution_id": execution_id,
            "logs": logs
        }

    except Exception as e:
        logger.error(f"获取执行日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/execution/{execution_id}/stop")
async def stop_execution(execution_id: str) -> Dict[str, Any]:
    """停止执行"""
    try:
        result = await pytest_execution_service.stop_execution(execution_id)

        return {
            "success": True,
            "execution_id": execution_id,
            "result": result,
            "message": "执行已停止"
        }

    except Exception as e:
        logger.error(f"停止执行失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
```

## 📊 实际应用示例

### 1. 自然语言用例示例

#### 示例1：登录功能测试
```
用例名称：用户登录功能测试

测试描述：
1. 打开应用
2. 点击登录按钮
3. 输入用户名"test_user"
4. 输入密码"123456"
5. 点击登录按钮
6. 验证登录成功，显示"欢迎回来"
7. 验证用户头像显示正常
```

#### 解析结果：
```json
{
  "test_intent": "验证用户登录功能的正确性",
  "preconditions": ["应用已安装", "网络连接正常"],
  "test_steps": [
    {
      "step_number": 1,
      "action": "app_start",
      "target": "目标应用",
      "description": "启动应用",
      "locator_type": "package",
      "locator_value": "com.example.app"
    },
    {
      "step_number": 2,
      "action": "click",
      "target": "登录按钮",
      "description": "点击登录按钮",
      "locator_type": "text",
      "locator_value": "登录"
    },
    {
      "step_number": 3,
      "action": "input",
      "target": "用户名输入框",
      "description": "输入用户名",
      "locator_type": "resource_id",
      "locator_value": "username",
      "input_value": "test_user"
    },
    {
      "step_number": 4,
      "action": "input",
      "target": "密码输入框",
      "description": "输入密码",
      "locator_type": "resource_id",
      "locator_value": "password",
      "input_value": "123456"
    },
    {
      "step_number": 5,
      "action": "click",
      "target": "登录按钮",
      "description": "点击登录按钮",
      "locator_type": "text",
      "locator_value": "登录"
    }
  ],
  "verification_points": [
    {
      "step_number": 6,
      "action": "verify_text",
      "target": "欢迎信息",
      "description": "验证登录成功提示",
      "expected_value": "欢迎回来"
    },
    {
      "step_number": 7,
      "action": "verify_element",
      "target": "用户头像",
      "description": "验证用户头像显示",
      "locator_type": "resource_id",
      "locator_value": "user_avatar"
    }
  ],
  "expected_results": ["成功登录到主页面", "显示用户信息"],
  "confidence_score": 0.92
}
```

#### 生成的pytest脚本：
```python
"""
自动生成的Android自动化测试脚本
测试用例：用户登录功能测试
生成时间：2025-01-XX XX:XX:XX
"""
import pytest
import uiautomator2 as u2
import allure
import time
from loguru import logger

@allure.epic("Android自动化测试")
@allure.feature("用户认证")
@allure.story("登录功能")
@pytest.mark.android
@pytest.mark.ui
@pytest.mark.smoke
class TestUserLogin:
    """用户登录功能测试"""

    @allure.title("验证用户登录功能的正确性")
    @allure.description("""
    测试用户登录功能，包括：
    1. 启动应用
    2. 输入用户名和密码
    3. 执行登录操作
    4. 验证登录结果
    """)
    @allure.severity(allure.severity_level.CRITICAL)
    def test_user_login_functionality(self, app_context):
        """
        验证用户登录功能的正确性

        前置条件：
        - 应用已安装
        - 网络连接正常

        测试步骤：
        1. 启动应用
        2. 点击登录按钮
        3. 输入用户名test_user
        4. 输入密码123456
        5. 点击登录按钮
        6. 验证登录成功提示
        7. 验证用户头像显示

        预期结果：
        - 成功登录到主页面
        - 显示用户信息
        """
        device = app_context

        try:
            # 步骤1: 启动应用
            with allure.step("启动应用 com.example.app"):
                device.app_start("com.example.app", stop=True)
                device.wait_activity(".MainActivity", timeout=10)
                logger.info("应用启动成功")

                # 截图记录初始状态
                screenshot = device.screenshot(format='raw')
                allure.attach(
                    screenshot,
                    name="应用启动后截图",
                    attachment_type=allure.attachment_type.PNG
                )

            # 步骤2: 点击登录按钮
            self._click_element(device, "text", "登录", "点击登录按钮")

            # 步骤3: 输入用户名
            self._input_text(device, "resource_id", "username", "test_user", "输入用户名")

            # 步骤4: 输入密码
            self._input_text(device, "resource_id", "password", "123456", "输入密码")

            # 步骤5: 点击登录按钮
            self._click_element(device, "text", "登录", "点击登录按钮执行登录")

            # 等待登录处理
            time.sleep(2)

            # 验证点6: 验证登录成功提示
            self._verify_text_exists(device, "欢迎回来", "验证登录成功提示")

            # 验证点7: 验证用户头像显示
            self._verify_element_exists(device, "resource_id", "user_avatar", "验证用户头像显示")

            # 记录成功状态
            with allure.step("登录测试完成"):
                screenshot = device.screenshot(format='raw')
                allure.attach(
                    screenshot,
                    name="登录成功截图",
                    attachment_type=allure.attachment_type.PNG
                )
                logger.info("用户登录功能测试通过")

        except Exception as e:
            logger.error(f"测试执行失败: {str(e)}")
            # 失败时截图
            screenshot = device.screenshot(format='raw')
            allure.attach(
                screenshot,
                name="失败截图",
                attachment_type=allure.attachment_type.PNG
            )
            raise

        finally:
            # 清理测试环境
            with allure.step("清理测试环境"):
                device.press("home")
                logger.info("测试环境清理完成")

    def _wait_for_element(self, device, locator_type, locator_value, timeout=10):
        """等待元素出现"""
        if locator_type == "text":
            return device(text=locator_value).wait(timeout=timeout)
        elif locator_type == "resource_id":
            return device(resourceId=locator_value).wait(timeout=timeout)
        elif locator_type == "description":
            return device(description=locator_value).wait(timeout=timeout)
        elif locator_type == "class_name":
            return device(className=locator_value).wait(timeout=timeout)
        else:
            raise ValueError(f"不支持的定位类型: {locator_type}")

    def _click_element(self, device, locator_type, locator_value, description=""):
        """点击元素"""
        with allure.step(f"点击元素: {description}"):
            # 等待元素出现
            self._wait_for_element(device, locator_type, locator_value)

            if locator_type == "text":
                device(text=locator_value).click()
            elif locator_type == "resource_id":
                device(resourceId=locator_value).click()
            elif locator_type == "description":
                device(description=locator_value).click()
            elif locator_type == "class_name":
                device(className=locator_value).click()
            else:
                raise ValueError(f"不支持的定位类型: {locator_type}")

            logger.info(f"点击元素成功: {description}")
            time.sleep(0.5)  # 等待UI响应

    def _input_text(self, device, locator_type, locator_value, text, description=""):
        """输入文本"""
        with allure.step(f"输入文本: {description}"):
            # 等待元素出现
            self._wait_for_element(device, locator_type, locator_value)

            if locator_type == "text":
                element = device(text=locator_value)
            elif locator_type == "resource_id":
                element = device(resourceId=locator_value)
            elif locator_type == "description":
                element = device(description=locator_value)
            elif locator_type == "class_name":
                element = device(className=locator_value)
            else:
                raise ValueError(f"不支持的定位类型: {locator_type}")

            element.clear_text()
            element.set_text(text)
            logger.info(f"输入文本成功: {description} = {text}")
            time.sleep(0.3)

    def _verify_text_exists(self, device, text, description=""):
        """验证文本存在"""
        with allure.step(f"验证文本存在: {description}"):
            exists = device(text=text).exists
            assert exists, f"文本不存在: {text}"
            logger.info(f"文本验证成功: {text}")

    def _verify_element_exists(self, device, locator_type, locator_value, description=""):
        """验证元素存在"""
        with allure.step(f"验证元素存在: {description}"):
            if locator_type == "text":
                exists = device(text=locator_value).exists
            elif locator_type == "resource_id":
                exists = device(resourceId=locator_value).exists
            elif locator_type == "description":
                exists = device(description=locator_value).exists
            elif locator_type == "class_name":
                exists = device(className=locator_value).exists
            else:
                raise ValueError(f"不支持的定位类型: {locator_type}")

            assert exists, f"元素不存在: {locator_value}"
            logger.info(f"元素验证成功: {description}")
```

### 2. 复杂场景示例

#### 示例2：购物车功能测试
```
用例名称：购物车添加商品测试

测试描述：
1. 打开购物应用
2. 搜索商品"手机"
3. 选择第一个商品
4. 点击加入购物车
5. 修改数量为2
6. 点击立即购买
7. 验证订单页面显示正确的商品和数量
8. 验证总价计算正确
```

#### 生成的复杂脚本片段：
```python
@allure.title("购物车添加商品功能测试")
@pytest.mark.android
@pytest.mark.regression
def test_shopping_cart_add_product(self, app_context):
    """购物车添加商品功能测试"""
    device = app_context

    try:
        # 启动购物应用
        with allure.step("启动购物应用"):
            device.app_start("com.example.shopping", stop=True)
            device.wait_activity(".MainActivity", timeout=10)

        # 搜索商品
        with allure.step("搜索商品'手机'"):
            # 点击搜索框
            device(resourceId="search_box").click()
            # 输入搜索关键词
            device(resourceId="search_input").set_text("手机")
            # 点击搜索按钮
            device(resourceId="search_button").click()
            # 等待搜索结果加载
            device(text="搜索结果").wait(timeout=10)

        # 选择第一个商品
        with allure.step("选择第一个商品"):
            # 等待商品列表加载
            device(resourceId="product_list").wait(timeout=10)
            # 点击第一个商品
            device(resourceId="product_list").child(index=0).click()
            # 等待商品详情页加载
            device(text="商品详情").wait(timeout=10)

        # 加入购物车
        with allure.step("点击加入购物车"):
            device(text="加入购物车").click()
            # 等待加入成功提示
            device(text="已加入购物车").wait(timeout=5)

        # 修改数量
        with allure.step("修改商品数量为2"):
            # 点击数量增加按钮
            device(resourceId="quantity_increase").click()
            # 验证数量显示为2
            quantity_text = device(resourceId="quantity_display").get_text()
            assert quantity_text == "2", f"数量显示错误，期望2，实际{quantity_text}"

        # 立即购买
        with allure.step("点击立即购买"):
            device(text="立即购买").click()
            # 等待订单页面加载
            device(text="确认订单").wait(timeout=10)

        # 验证订单信息
        with allure.step("验证订单页面信息"):
            # 验证商品名称包含"手机"
            product_name = device(resourceId="order_product_name").get_text()
            assert "手机" in product_name, f"商品名称错误: {product_name}"

            # 验证数量为2
            order_quantity = device(resourceId="order_quantity").get_text()
            assert "2" in order_quantity, f"订单数量错误: {order_quantity}"

            # 验证总价计算（假设单价100元）
            total_price = device(resourceId="total_price").get_text()
            # 提取数字部分进行验证
            import re
            price_match = re.search(r'(\d+)', total_price)
            if price_match:
                price_value = int(price_match.group(1))
                assert price_value >= 200, f"总价计算错误: {total_price}"

        logger.info("购物车添加商品测试通过")

    except Exception as e:
        logger.error(f"购物车测试失败: {str(e)}")
        raise
```

## 🔧 高级功能实现

### 1. 智能元素定位

```python
"""
智能元素定位器
基于AI模型智能识别和定位Android UI元素
"""
import cv2
import numpy as np
from typing import Dict, List, Tuple, Optional
from loguru import logger

class SmartElementLocator:
    """智能元素定位器"""

    def __init__(self, device):
        self.device = device
        self.ui_tars_client = None  # UI-TARS模型客户端

    async def smart_locate_element(
        self,
        description: str,
        screenshot: Optional[np.ndarray] = None
    ) -> Dict[str, Any]:
        """智能定位元素"""
        try:
            if screenshot is None:
                screenshot = self.device.screenshot(format='opencv')

            # 获取UI层次结构
            ui_hierarchy = self.device.dump_hierarchy()

            # 使用AI模型分析
            analysis_result = await self._analyze_with_ai(
                screenshot, ui_hierarchy, description
            )

            # 生成定位策略
            locator_strategies = self._generate_locator_strategies(analysis_result)

            # 验证定位策略
            best_locator = await self._validate_locators(locator_strategies)

            return best_locator

        except Exception as e:
            logger.error(f"智能元素定位失败: {str(e)}")
            return self._fallback_locator(description)

    async def _analyze_with_ai(
        self,
        screenshot: np.ndarray,
        ui_hierarchy: str,
        description: str
    ) -> Dict[str, Any]:
        """使用AI模型分析元素"""
        # 调用UI-TARS模型进行分析
        prompt = f"""
        分析这个Android界面截图，找到描述为"{description}"的UI元素。

        请返回JSON格式的分析结果，包含：
        - element_type: 元素类型
        - coordinates: 元素坐标
        - attributes: 元素属性
        - confidence: 置信度
        - locator_suggestions: 定位建议

        UI层次结构：
        {ui_hierarchy}
        """

        # 这里调用实际的AI模型
        # analysis_result = await self.ui_tars_client.analyze(screenshot, prompt)

        # 模拟返回结果
        return {
            "element_type": "button",
            "coordinates": {"x": 540, "y": 960},
            "attributes": {
                "text": "登录",
                "resource_id": "login_button",
                "class_name": "android.widget.Button"
            },
            "confidence": 0.95,
            "locator_suggestions": [
                {"type": "text", "value": "登录", "priority": 1},
                {"type": "resource_id", "value": "login_button", "priority": 2},
                {"type": "coordinates", "value": (540, 960), "priority": 3}
            ]
        }

    def _generate_locator_strategies(self, analysis_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成定位策略"""
        strategies = []

        for suggestion in analysis_result.get("locator_suggestions", []):
            strategy = {
                "type": suggestion["type"],
                "value": suggestion["value"],
                "priority": suggestion["priority"],
                "confidence": analysis_result.get("confidence", 0.5)
            }
            strategies.append(strategy)

        # 按优先级排序
        strategies.sort(key=lambda x: x["priority"])

        return strategies

    async def _validate_locators(self, strategies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证定位策略"""
        for strategy in strategies:
            try:
                if strategy["type"] == "text":
                    element = self.device(text=strategy["value"])
                elif strategy["type"] == "resource_id":
                    element = self.device(resourceId=strategy["value"])
                elif strategy["type"] == "coordinates":
                    x, y = strategy["value"]
                    # 坐标定位需要特殊处理
                    return {
                        "type": "coordinates",
                        "value": (x, y),
                        "confidence": strategy["confidence"]
                    }
                else:
                    continue

                # 检查元素是否存在
                if element.exists:
                    return {
                        "type": strategy["type"],
                        "value": strategy["value"],
                        "confidence": strategy["confidence"]
                    }

            except Exception as e:
                logger.warning(f"定位策略验证失败: {strategy}, 错误: {str(e)}")
                continue

        # 如果所有策略都失败，返回默认策略
        return self._fallback_locator("未知元素")

    def _fallback_locator(self, description: str) -> Dict[str, Any]:
        """回退定位策略"""
        return {
            "type": "description",
            "value": description,
            "confidence": 0.3,
            "fallback": True
        }
```

### 2. 自适应等待机制

```python
"""
自适应等待机制
根据应用性能和网络状况动态调整等待时间
"""
import time
import statistics
from typing import List, Dict, Any
from loguru import logger

class AdaptiveWaiter:
    """自适应等待器"""

    def __init__(self, device):
        self.device = device
        self.response_times: List[float] = []
        self.network_delays: List[float] = []
        self.base_timeout = 10.0
        self.max_timeout = 30.0
        self.min_timeout = 2.0

    async def smart_wait_for_element(
        self,
        locator_type: str,
        locator_value: str,
        description: str = ""
    ) -> bool:
        """智能等待元素出现"""
        try:
            # 计算动态超时时间
            timeout = self._calculate_dynamic_timeout()

            logger.info(f"等待元素出现: {description}, 超时时间: {timeout}s")

            start_time = time.time()

            # 执行等待
            if locator_type == "text":
                result = self.device(text=locator_value).wait(timeout=timeout)
            elif locator_type == "resource_id":
                result = self.device(resourceId=locator_value).wait(timeout=timeout)
            elif locator_type == "description":
                result = self.device(description=locator_value).wait(timeout=timeout)
            else:
                result = False

            # 记录响应时间
            response_time = time.time() - start_time
            self._record_response_time(response_time)

            if result:
                logger.info(f"元素出现成功: {description}, 耗时: {response_time:.2f}s")
            else:
                logger.warning(f"元素等待超时: {description}, 耗时: {response_time:.2f}s")

            return result

        except Exception as e:
            logger.error(f"智能等待失败: {str(e)}")
            return False

    def _calculate_dynamic_timeout(self) -> float:
        """计算动态超时时间"""
        if not self.response_times:
            return self.base_timeout

        # 计算平均响应时间和标准差
        avg_response = statistics.mean(self.response_times[-10:])  # 最近10次
        std_response = statistics.stdev(self.response_times[-10:]) if len(self.response_times) > 1 else 0

        # 动态调整超时时间
        dynamic_timeout = avg_response + (2 * std_response) + 2.0  # 2倍标准差 + 2秒缓冲

        # 限制在合理范围内
        dynamic_timeout = max(self.min_timeout, min(dynamic_timeout, self.max_timeout))

        return dynamic_timeout

    def _record_response_time(self, response_time: float):
        """记录响应时间"""
        self.response_times.append(response_time)

        # 只保留最近50次记录
        if len(self.response_times) > 50:
            self.response_times = self.response_times[-50:]

    async def smart_wait_for_page_load(self, page_indicator: str) -> bool:
        """智能等待页面加载"""
        try:
            # 检测页面加载指标
            indicators = [
                {"type": "text", "value": page_indicator},
                {"type": "resource_id", "value": "loading_indicator"},
                {"type": "class_name", "value": "android.widget.ProgressBar"}
            ]

            # 等待加载指示器消失
            for indicator in indicators:
                if indicator["type"] == "text":
                    element = self.device(text=indicator["value"])
                elif indicator["type"] == "resource_id":
                    element = self.device(resourceId=indicator["value"])
                elif indicator["type"] == "class_name":
                    element = self.device(className=indicator["value"])
                else:
                    continue

                if element.exists:
                    # 等待加载指示器消失
                    element.wait_gone(timeout=self._calculate_dynamic_timeout())

            # 等待页面稳定
            await self._wait_for_page_stable()

            return True

        except Exception as e:
            logger.error(f"页面加载等待失败: {str(e)}")
            return False

    async def _wait_for_page_stable(self, stable_duration: float = 1.0):
        """等待页面稳定"""
        try:
            last_hierarchy = ""
            stable_start = None

            while True:
                current_hierarchy = self.device.dump_hierarchy()

                if current_hierarchy == last_hierarchy:
                    if stable_start is None:
                        stable_start = time.time()
                    elif time.time() - stable_start >= stable_duration:
                        logger.info("页面已稳定")
                        break
                else:
                    stable_start = None
                    last_hierarchy = current_hierarchy

                time.sleep(0.5)

        except Exception as e:
            logger.warning(f"页面稳定性检测失败: {str(e)}")
```

## 🚀 部署和运维方案

### 1. 环境准备

#### 系统要求
```bash
# 操作系统要求
- Ubuntu 20.04+ / CentOS 8+ / macOS 12+
- Windows 10+ (WSL2推荐)

# 软件依赖
- Python 3.9+
- Node.js 18+
- Android SDK Platform Tools
- Java 8+ (Android SDK依赖)
- Docker 20.10+ (可选)
```

#### 安装脚本
```bash
#!/bin/bash
# install-android-automation.sh - Android自动化环境安装脚本

set -e

echo "🚀 开始安装Android自动化测试环境..."

# 检查Python版本
check_python() {
    echo "📋 检查Python环境..."
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3未安装，请先安装Python 3.9+"
        exit 1
    fi

    python_version=$(python3 -c "import sys; print('.'.join(map(str, sys.version_info[:2])))")
    if [[ $(echo "$python_version < 3.9" | bc -l) -eq 1 ]]; then
        echo "❌ Python版本过低，当前版本: $python_version，需要3.9+"
        exit 1
    fi

    echo "✅ Python版本检查通过: $python_version"
}

# 安装Android SDK
install_android_sdk() {
    echo "📱 安装Android SDK..."

    # 检查是否已安装
    if command -v adb &> /dev/null; then
        echo "✅ Android SDK已安装"
        return
    fi

    # 下载并安装Android SDK
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        SDK_URL="https://dl.google.com/android/repository/commandlinetools-linux-8512546_latest.zip"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        SDK_URL="https://dl.google.com/android/repository/commandlinetools-mac-8512546_latest.zip"
    else
        echo "❌ 不支持的操作系统: $OSTYPE"
        exit 1
    fi

    # 创建Android SDK目录
    ANDROID_HOME="$HOME/android-sdk"
    mkdir -p "$ANDROID_HOME"

    # 下载并解压
    cd /tmp
    wget -O commandlinetools.zip "$SDK_URL"
    unzip commandlinetools.zip -d "$ANDROID_HOME"

    # 设置环境变量
    echo "export ANDROID_HOME=$ANDROID_HOME" >> ~/.bashrc
    echo "export PATH=\$PATH:\$ANDROID_HOME/cmdline-tools/bin:\$ANDROID_HOME/platform-tools" >> ~/.bashrc
    source ~/.bashrc

    # 安装platform-tools
    yes | "$ANDROID_HOME/cmdline-tools/bin/sdkmanager" --sdk_root="$ANDROID_HOME" "platform-tools"

    echo "✅ Android SDK安装完成"
}

# 安装Python依赖
install_python_deps() {
    echo "🐍 安装Python依赖..."

    # 创建虚拟环境
    python3 -m venv venv
    source venv/bin/activate

    # 升级pip
    pip install --upgrade pip

    # 安装核心依赖
    pip install uiautomator2>=3.4.0
    pip install pytest>=7.0.0
    pip install allure-pytest>=2.12.0
    pip install fastapi>=0.100.0
    pip install uvicorn>=0.23.0
    pip install loguru>=0.6.0
    pip install Pillow>=9.0.0
    pip install opencv-python>=4.8.0
    pip install numpy>=1.24.0

    echo "✅ Python依赖安装完成"
}

# 安装allure命令行工具
install_allure() {
    echo "📊 安装Allure报告工具..."

    if command -v allure &> /dev/null; then
        echo "✅ Allure已安装"
        return
    fi

    # 下载并安装allure
    ALLURE_VERSION="2.24.0"
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        ALLURE_URL="https://github.com/allure-framework/allure2/releases/download/${ALLURE_VERSION}/allure-${ALLURE_VERSION}.tgz"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS使用Homebrew安装
        if command -v brew &> /dev/null; then
            brew install allure
            echo "✅ Allure安装完成"
            return
        else
            ALLURE_URL="https://github.com/allure-framework/allure2/releases/download/${ALLURE_VERSION}/allure-${ALLURE_VERSION}.tgz"
        fi
    fi

    # 下载并安装
    cd /tmp
    wget -O allure.tgz "$ALLURE_URL"
    tar -xzf allure.tgz
    sudo mv allure-${ALLURE_VERSION} /opt/allure
    sudo ln -sf /opt/allure/bin/allure /usr/local/bin/allure

    echo "✅ Allure安装完成"
}

# 验证安装
verify_installation() {
    echo "🔍 验证安装..."

    # 检查adb
    if ! command -v adb &> /dev/null; then
        echo "❌ ADB未正确安装"
        exit 1
    fi

    # 检查Python包
    python3 -c "import uiautomator2; print('✅ uiautomator2:', uiautomator2.__version__)"
    python3 -c "import pytest; print('✅ pytest:', pytest.__version__)"
    python3 -c "import allure; print('✅ allure-pytest: OK')"

    # 检查allure
    if ! command -v allure &> /dev/null; then
        echo "❌ Allure命令行工具未正确安装"
        exit 1
    fi

    allure_version=$(allure --version)
    echo "✅ Allure: $allure_version"

    echo "🎉 所有组件安装验证通过！"
}

# 主安装流程
main() {
    check_python
    install_android_sdk
    install_python_deps
    install_allure
    verify_installation

    echo ""
    echo "🎉 Android自动化测试环境安装完成！"
    echo ""
    echo "📝 下一步操作："
    echo "1. 连接Android设备并启用USB调试"
    echo "2. 运行 'adb devices' 验证设备连接"
    echo "3. 启动自动化测试服务"
    echo ""
}

main "$@"
```

### 2. Docker部署方案

#### Dockerfile
```dockerfile
# Dockerfile.android-automation
FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV ANDROID_HOME=/opt/android-sdk
ENV PATH=$PATH:$ANDROID_HOME/cmdline-tools/bin:$ANDROID_HOME/platform-tools

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    wget \
    unzip \
    openjdk-11-jdk \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 安装Android SDK
RUN mkdir -p $ANDROID_HOME && \
    cd /tmp && \
    wget https://dl.google.com/android/repository/commandlinetools-linux-8512546_latest.zip && \
    unzip commandlinetools-linux-8512546_latest.zip -d $ANDROID_HOME && \
    yes | $ANDROID_HOME/cmdline-tools/bin/sdkmanager --sdk_root=$ANDROID_HOME "platform-tools"

# 安装Allure
RUN cd /tmp && \
    wget https://github.com/allure-framework/allure2/releases/download/2.24.0/allure-2.24.0.tgz && \
    tar -xzf allure-2.24.0.tgz && \
    mv allure-2.24.0 /opt/allure && \
    ln -sf /opt/allure/bin/allure /usr/local/bin/allure

# 设置工作目录
WORKDIR /app

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip3 install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p /app/test_executions /app/allure-reports /app/logs

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  android-automation:
    build:
      context: .
      dockerfile: Dockerfile.android-automation
    ports:
      - "8000:8000"
    volumes:
      - ./test_executions:/app/test_executions
      - ./allure-reports:/app/allure-reports
      - ./logs:/app/logs
      - /dev/bus/usb:/dev/bus/usb  # USB设备访问
    privileged: true  # 需要访问USB设备
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
    networks:
      - automation-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - automation-network

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: android_automation
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - automation-network

  allure-server:
    image: frankescobar/allure-docker-service
    ports:
      - "5050:5050"
    volumes:
      - ./allure-reports:/app/allure-results
    environment:
      CHECK_RESULTS_EVERY_SECONDS: 3
      KEEP_HISTORY: 20
    networks:
      - automation-network

volumes:
  mysql_data:
  redis_data:

networks:
  automation-network:
    driver: bridge
```

### 3. CI/CD集成

#### GitHub Actions工作流
```yaml
# .github/workflows/android-automation.yml
name: Android自动化测试

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 2 * * *'  # 每天凌晨2点执行

jobs:
  android-automation-test:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: android_automation
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: 安装系统依赖
      run: |
        sudo apt-get update
        sudo apt-get install -y openjdk-11-jdk wget unzip

    - name: 安装Android SDK
      run: |
        mkdir -p $HOME/android-sdk
        cd /tmp
        wget https://dl.google.com/android/repository/commandlinetools-linux-8512546_latest.zip
        unzip commandlinetools-linux-8512546_latest.zip -d $HOME/android-sdk
        echo "ANDROID_HOME=$HOME/android-sdk" >> $GITHUB_ENV
        echo "$HOME/android-sdk/cmdline-tools/bin:$HOME/android-sdk/platform-tools" >> $GITHUB_PATH
        yes | $HOME/android-sdk/cmdline-tools/bin/sdkmanager --sdk_root=$HOME/android-sdk "platform-tools"

    - name: 安装Python依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: 安装Allure
      run: |
        cd /tmp
        wget https://github.com/allure-framework/allure2/releases/download/2.24.0/allure-2.24.0.tgz
        tar -xzf allure-2.24.0.tgz
        sudo mv allure-2.24.0 /opt/allure
        sudo ln -sf /opt/allure/bin/allure /usr/local/bin/allure

    - name: 启动Android模拟器
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: 29
        target: google_apis
        arch: x86_64
        profile: Nexus 6
        script: |
          adb devices
          adb shell input keyevent 82  # 解锁屏幕

    - name: 运行自动化测试
      run: |
        # 启动测试服务
        uvicorn app.main:app --host 0.0.0.0 --port 8000 &
        sleep 10

        # 运行测试用例
        python -m pytest tests/android/ -v --allure-dir=allure-results
      env:
        ANDROID_SERIAL: emulator-5554
        DATABASE_URL: mysql://root:password@localhost:3306/android_automation

    - name: 生成Allure报告
      if: always()
      run: |
        allure generate allure-results -o allure-report --clean

    - name: 上传Allure报告
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: allure-report
        path: allure-report/

    - name: 上传测试截图
      if: failure()
      uses: actions/upload-artifact@v3
      with:
        name: test-screenshots
        path: test_executions/*/screenshots/
```

## 🧪 测试策略和质量保证

### 1. 单元测试

#### 自然语言解析测试
```python
"""
自然语言解析功能单元测试
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch

from app.agents.android.nl_parser_agent import NLParserAgent
from app.core.messages.android import NLParsingRequest

class TestNLParserAgent:
    """自然语言解析智能体测试"""

    @pytest.fixture
    def mock_model_client(self):
        """模拟AI模型客户端"""
        client = Mock()
        client.create_completion = AsyncMock(return_value=Mock(
            choices=[Mock(message=Mock(content='''{
                "test_intent": "验证登录功能",
                "test_steps": [
                    {
                        "step_number": 1,
                        "action": "click",
                        "target": "登录按钮",
                        "description": "点击登录按钮"
                    }
                ],
                "verification_points": [],
                "preconditions": ["应用已安装"],
                "expected_results": ["登录成功"]
            }'''))]
        ))
        return client

    @pytest.fixture
    def nl_parser_agent(self, mock_model_client):
        """创建自然语言解析智能体实例"""
        return NLParserAgent(model_client=mock_model_client)

    @pytest.mark.asyncio
    async def test_parse_simple_login_case(self, nl_parser_agent):
        """测试简单登录用例解析"""
        request = NLParsingRequest(
            session_id="test_session",
            case_name="登录测试",
            nl_content="1. 点击登录按钮\n2. 输入用户名\n3. 输入密码\n4. 点击登录",
            target_app="com.example.app"
        )

        result = await nl_parser_agent.handle_parsing_request(request, Mock())

        assert result is not None
        assert result.test_intent == "验证登录功能"
        assert len(result.test_steps) == 1
        assert result.test_steps[0]["action"] == "click"
        assert result.confidence_score > 0.5

    @pytest.mark.asyncio
    async def test_parse_complex_shopping_case(self, nl_parser_agent):
        """测试复杂购物用例解析"""
        request = NLParsingRequest(
            session_id="test_session",
            case_name="购物测试",
            nl_content="""
            1. 打开购物应用
            2. 搜索商品"手机"
            3. 选择第一个商品
            4. 加入购物车
            5. 修改数量为2
            6. 结算订单
            7. 验证订单信息正确
            """,
            target_app="com.example.shopping"
        )

        result = await nl_parser_agent.handle_parsing_request(request, Mock())

        assert result is not None
        assert "购物" in result.test_intent or "商品" in result.test_intent
        assert len(result.test_steps) >= 1
        assert result.confidence_score > 0.3

    def test_confidence_calculation(self, nl_parser_agent):
        """测试置信度计算"""
        parsed_data = {
            "test_intent": "测试登录",
            "test_steps": [
                {
                    "action": "click",
                    "target": "按钮",
                    "description": "点击按钮"
                }
            ],
            "verification_points": [
                {
                    "action": "verify",
                    "description": "验证结果"
                }
            ]
        }

        confidence = nl_parser_agent._calculate_parsing_confidence(parsed_data)

        assert 0.0 <= confidence <= 1.0
        assert confidence > 0.5  # 有基本字段应该有较高置信度
```

#### 脚本生成测试
```python
"""
Android脚本生成功能单元测试
"""
import pytest
from unittest.mock import Mock, AsyncMock

from app.agents.android.script_generator_agent import AndroidScriptGeneratorAgent
from app.core.messages.android import AndroidScriptGenerationRequest

class TestAndroidScriptGeneratorAgent:
    """Android脚本生成智能体测试"""

    @pytest.fixture
    def mock_model_client(self):
        """模拟AI模型客户端"""
        client = Mock()
        client.create_completion = AsyncMock(return_value=Mock(
            choices=[Mock(message=Mock(content='''
import pytest
import uiautomator2 as u2
import allure

@pytest.mark.android
def test_login(app_context):
    device = app_context
    device(text="登录").click()
    assert device(text="欢迎").exists
            '''))]
        ))
        return client

    @pytest.fixture
    def script_generator_agent(self, mock_model_client):
        """创建脚本生成智能体实例"""
        return AndroidScriptGeneratorAgent(model_client=mock_model_client)

    @pytest.mark.asyncio
    async def test_generate_login_script(self, script_generator_agent):
        """测试登录脚本生成"""
        request = AndroidScriptGenerationRequest(
            session_id="test_session",
            case_name="登录测试",
            test_intent="验证登录功能",
            test_steps=[
                {
                    "step_number": 1,
                    "action": "click",
                    "target": "登录按钮",
                    "locator_type": "text",
                    "locator_value": "登录",
                    "description": "点击登录按钮"
                }
            ],
            verification_points=[
                {
                    "step_number": 2,
                    "action": "verify_text",
                    "target": "欢迎信息",
                    "expected_value": "欢迎",
                    "description": "验证登录成功"
                }
            ],
            target_app="com.example.app"
        )

        result = await script_generator_agent.handle_script_generation(request, Mock())

        assert result is not None
        assert result.format == "uiautomator2-pytest"
        assert "import pytest" in result.content
        assert "import uiautomator2" in result.content
        assert "import allure" in result.content
        assert "@pytest.mark.android" in result.content

    def test_project_files_generation(self, script_generator_agent):
        """测试项目文件生成"""
        script_content = "test script content"
        request = AndroidScriptGenerationRequest(
            session_id="test_session",
            case_name="测试用例",
            test_intent="测试意图",
            test_steps=[],
            verification_points=[],
            target_app="com.example.app"
        )

        project_files = script_generator_agent._generate_project_files(script_content, request)

        assert "test_script.py" in project_files
        assert "conftest.py" in project_files
        assert "pytest.ini" in project_files
        assert "requirements.txt" in project_files
        assert "README.md" in project_files

        # 验证conftest.py内容
        conftest_content = project_files["conftest.py"]
        assert "import pytest" in conftest_content
        assert "import uiautomator2" in conftest_content
        assert "@pytest.fixture" in conftest_content
```

### 2. 集成测试

#### 端到端测试流程
```python
"""
Android自动化端到端集成测试
验证完整的自然语言到脚本执行流程
"""
import pytest
import asyncio
from fastapi.testclient import TestClient

from app.main import app

class TestAndroidAutomationE2E:
    """Android自动化端到端测试"""

    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)

    @pytest.fixture
    def sample_nl_case(self):
        """示例自然语言用例"""
        return {
            "session_id": "test_session_e2e",
            "case_name": "登录功能测试",
            "nl_content": """
            1. 打开应用
            2. 点击登录按钮
            3. 输入用户名test_user
            4. 输入密码123456
            5. 点击登录
            6. 验证登录成功
            """,
            "target_app": "com.example.testapp",
            "device_requirements": {}
        }

    def test_complete_automation_workflow(self, client, sample_nl_case):
        """测试完整的自动化工作流程"""

        # 步骤1: 解析自然语言用例
        response = client.post("/android/nl/parse", json=sample_nl_case)
        assert response.status_code == 200
        parse_result = response.json()
        assert parse_result["success"] is True

        parsed_data = parse_result["parsed_result"]
        assert "test_intent" in parsed_data
        assert len(parsed_data["test_steps"]) > 0

        # 步骤2: 生成Android脚本
        script_request = {
            "session_id": sample_nl_case["session_id"],
            "case_name": sample_nl_case["case_name"],
            "test_intent": parsed_data["test_intent"],
            "test_steps": parsed_data["test_steps"],
            "verification_points": parsed_data.get("verification_points", []),
            "preconditions": parsed_data.get("preconditions", []),
            "expected_results": parsed_data.get("expected_results", []),
            "target_app": sample_nl_case["target_app"]
        }

        response = client.post("/android/script/generate", json=script_request)
        assert response.status_code == 200
        script_result = response.json()
        assert script_result["success"] is True

        generated_script = script_result["generated_script"]
        assert generated_script["format"] == "uiautomator2-pytest"
        assert "import pytest" in generated_script["content"]

        # 步骤3: 验证脚本语法
        validation_request = {
            "script_content": generated_script["content"],
            "target_app": sample_nl_case["target_app"]
        }

        response = client.post("/android/script/validate", json=validation_request)
        assert response.status_code == 200
        validation_result = response.json()
        assert validation_result["success"] is True

        # 步骤4: 模拟脚本执行（在有设备的情况下）
        if self._has_connected_device():
            execution_request = {
                "execution_id": f"exec_{sample_nl_case['session_id']}",
                "script_content": generated_script["content"],
                "case_name": sample_nl_case["case_name"],
                "target_app": sample_nl_case["target_app"]
            }

            response = client.post("/android/pytest/execute", json=execution_request)
            assert response.status_code == 200
            execution_result = response.json()
            assert execution_result["success"] is True

    def _has_connected_device(self) -> bool:
        """检查是否有连接的Android设备"""
        try:
            import subprocess
            result = subprocess.run(["adb", "devices"], capture_output=True, text=True)
            return "device" in result.stdout
        except:
            return False

    def test_error_handling(self, client):
        """测试错误处理"""

        # 测试无效的自然语言用例
        invalid_case = {
            "session_id": "test_error",
            "case_name": "",  # 空名称
            "nl_content": "",  # 空内容
            "target_app": "com.example.app"
        }

        response = client.post("/android/nl/parse", json=invalid_case)
        # 应该返回错误或处理空内容
        assert response.status_code in [400, 422, 500]

        # 测试无效的脚本生成请求
        invalid_script_request = {
            "session_id": "test_error",
            "case_name": "测试",
            "test_intent": "",
            "test_steps": [],  # 空步骤
            "verification_points": [],
            "target_app": "com.example.app"
        }

        response = client.post("/android/script/generate", json=invalid_script_request)
        # 应该能处理空步骤的情况
        assert response.status_code in [200, 400, 422]

    def test_concurrent_requests(self, client, sample_nl_case):
        """测试并发请求处理"""
        import threading
        import time

        results = []

        def make_request():
            try:
                response = client.post("/android/nl/parse", json=sample_nl_case)
                results.append(response.status_code)
            except Exception as e:
                results.append(str(e))

        # 创建多个并发请求
        threads = []
        for i in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()

        # 等待所有请求完成
        for thread in threads:
            thread.join()

        # 验证所有请求都成功处理
        assert len(results) == 5
        assert all(result == 200 for result in results if isinstance(result, int))
```

## 📈 性能优化和最佳实践

### 1. 性能优化策略

#### 脚本执行优化
```python
"""
Android脚本执行性能优化
"""
import time
import asyncio
from typing import Dict, Any, List
from concurrent.futures import ThreadPoolExecutor
from loguru import logger

class PerformanceOptimizer:
    """性能优化器"""

    def __init__(self):
        self.execution_cache = {}
        self.element_cache = {}
        self.thread_pool = ThreadPoolExecutor(max_workers=4)

    async def optimize_script_execution(
        self,
        script_content: str,
        optimization_level: str = "balanced"
    ) -> str:
        """优化脚本执行性能"""
        try:
            optimizations = {
                "fast": self._apply_fast_optimizations,
                "balanced": self._apply_balanced_optimizations,
                "stable": self._apply_stable_optimizations
            }

            optimizer = optimizations.get(optimization_level, self._apply_balanced_optimizations)
            optimized_script = await optimizer(script_content)

            logger.info(f"脚本优化完成，优化级别: {optimization_level}")
            return optimized_script

        except Exception as e:
            logger.error(f"脚本优化失败: {str(e)}")
            return script_content

    async def _apply_fast_optimizations(self, script_content: str) -> str:
        """应用快速优化"""
        optimizations = [
            # 减少等待时间
            ("time.sleep(2)", "time.sleep(0.5)"),
            ("time.sleep(1)", "time.sleep(0.3)"),
            ("timeout=10", "timeout=5"),
            ("timeout=15", "timeout=8"),

            # 使用更快的定位方式
            ("device(text=", "device(textContains="),
            ("wait(timeout=", "wait(timeout="),

            # 并行化操作
            ("# 截图记录", "# 跳过截图以提高速度"),
        ]

        optimized = script_content
        for old, new in optimizations:
            optimized = optimized.replace(old, new)

        # 添加性能监控
        optimized = self._add_performance_monitoring(optimized, "fast")

        return optimized

    async def _apply_balanced_optimizations(self, script_content: str) -> str:
        """应用平衡优化"""
        optimizations = [
            # 适中的等待时间
            ("time.sleep(2)", "time.sleep(1)"),
            ("timeout=10", "timeout=8"),

            # 智能等待
            ("device(text=", "self._smart_wait_and_find(device, 'text',"),

            # 缓存元素
            ("device(resourceId=", "self._cached_find(device, 'resourceId',"),
        ]

        optimized = script_content
        for old, new in optimizations:
            optimized = optimized.replace(old, new)

        # 添加智能等待方法
        optimized = self._add_smart_wait_methods(optimized)
        optimized = self._add_performance_monitoring(optimized, "balanced")

        return optimized

    async def _apply_stable_optimizations(self, script_content: str) -> str:
        """应用稳定优化"""
        optimizations = [
            # 增加重试机制
            ("device(text=", "self._retry_find(device, 'text',"),
            ("click()", "click_with_retry()"),

            # 增加验证
            ("assert ", "self._safe_assert("),

            # 更多的等待时间
            ("timeout=5", "timeout=10"),
            ("time.sleep(0.5)", "time.sleep(1)"),
        ]

        optimized = script_content
        for old, new in optimizations:
            optimized = optimized.replace(old, new)

        # 添加重试和错误恢复方法
        optimized = self._add_retry_methods(optimized)
        optimized = self._add_performance_monitoring(optimized, "stable")

        return optimized

    def _add_smart_wait_methods(self, script_content: str) -> str:
        """添加智能等待方法"""
        smart_methods = '''
    def _smart_wait_and_find(self, device, locator_type, locator_value, timeout=8):
        """智能等待并查找元素"""
        cache_key = f"{locator_type}:{locator_value}"

        # 检查缓存
        if cache_key in self.element_cache:
            cached_time, cached_result = self.element_cache[cache_key]
            if time.time() - cached_time < 30:  # 30秒缓存
                return cached_result

        # 查找元素
        if locator_type == "text":
            element = device(text=locator_value)
        elif locator_type == "resourceId":
            element = device(resourceId=locator_value)
        else:
            element = device(**{locator_type: locator_value})

        # 等待元素出现
        if element.wait(timeout=timeout):
            self.element_cache[cache_key] = (time.time(), element)
            return element
        else:
            raise Exception(f"元素未找到: {locator_type}={locator_value}")

    def _cached_find(self, device, locator_type, locator_value):
        """缓存查找元素"""
        return self._smart_wait_and_find(device, locator_type, locator_value)
'''

        # 在类定义后添加方法
        class_end = script_content.rfind("class Test")
        if class_end != -1:
            next_class = script_content.find("\nclass ", class_end + 1)
            if next_class == -1:
                next_class = len(script_content)

            insert_pos = script_content.rfind("\n", class_end, next_class)
            if insert_pos != -1:
                script_content = (script_content[:insert_pos] +
                                smart_methods +
                                script_content[insert_pos:])

        return script_content

    def _add_retry_methods(self, script_content: str) -> str:
        """添加重试方法"""
        retry_methods = '''
    def _retry_find(self, device, locator_type, locator_value, max_retries=3):
        """重试查找元素"""
        for attempt in range(max_retries):
            try:
                if locator_type == "text":
                    element = device(text=locator_value)
                elif locator_type == "resourceId":
                    element = device(resourceId=locator_value)
                else:
                    element = device(**{locator_type: locator_value})

                if element.exists:
                    return element

            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                time.sleep(1)

        raise Exception(f"重试{max_retries}次后仍未找到元素: {locator_type}={locator_value}")

    def click_with_retry(self, max_retries=3):
        """带重试的点击"""
        for attempt in range(max_retries):
            try:
                self.click()
                return True
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                time.sleep(0.5)
        return False

    def _safe_assert(self, condition, message="断言失败"):
        """安全断言"""
        try:
            assert condition, message
        except AssertionError as e:
            logger.error(f"断言失败: {message}")
            # 截图记录失败状态
            screenshot = self.device.screenshot(format='raw')
            allure.attach(screenshot, name="断言失败截图", attachment_type=allure.attachment_type.PNG)
            raise e
'''

        # 在类定义后添加方法
        class_end = script_content.rfind("class Test")
        if class_end != -1:
            next_class = script_content.find("\nclass ", class_end + 1)
            if next_class == -1:
                next_class = len(script_content)

            insert_pos = script_content.rfind("\n", class_end, next_class)
            if insert_pos != -1:
                script_content = (script_content[:insert_pos] +
                                retry_methods +
                                script_content[insert_pos:])

        return script_content

    def _add_performance_monitoring(self, script_content: str, level: str) -> str:
        """添加性能监控"""
        monitoring_code = f'''
    def setUp(self):
        """测试前置设置"""
        self.start_time = time.time()
        self.optimization_level = "{level}"
        logger.info(f"开始执行测试，优化级别: {level}")

    def tearDown(self):
        """测试后置清理"""
        execution_time = time.time() - self.start_time
        logger.info(f"测试执行完成，耗时: {{execution_time:.2f}}秒，优化级别: {level}")

        # 记录性能指标
        allure.attach(
            f"执行时间: {{execution_time:.2f}}秒\\n优化级别: {level}",
            name="性能指标",
            attachment_type=allure.attachment_type.TEXT
        )
'''

        # 在类定义后添加方法
        class_end = script_content.rfind("class Test")
        if class_end != -1:
            next_class = script_content.find("\nclass ", class_end + 1)
            if next_class == -1:
                next_class = len(script_content)

            insert_pos = script_content.rfind("\n", class_end, next_class)
            if insert_pos != -1:
                script_content = (script_content[:insert_pos] +
                                monitoring_code +
                                script_content[insert_pos:])

        return script_content
```

### 2. 最佳实践指南

#### 自然语言用例编写规范
```markdown
# 自然语言测试用例编写最佳实践

## 1. 用例结构规范

### 标准格式
```
用例名称：[功能模块] + [具体操作] + 测试

测试描述：
前置条件：
- 条件1
- 条件2

测试步骤：
1. 操作步骤1
2. 操作步骤2
3. 验证步骤

预期结果：
- 结果1
- 结果2
```

### 示例
```
用例名称：用户登录功能测试

测试描述：验证用户使用正确的用户名和密码能够成功登录系统

前置条件：
- 应用已安装并启动
- 网络连接正常
- 用户账号已注册

测试步骤：
1. 在登录页面点击用户名输入框
2. 输入有效用户名"test_user"
3. 点击密码输入框
4. 输入正确密码"123456"
5. 点击登录按钮
6. 验证页面跳转到主页
7. 验证显示用户欢迎信息

预期结果：
- 成功登录到主页面
- 显示"欢迎回来，test_user"信息
- 用户头像正常显示
```

## 2. 操作描述规范

### 点击操作
- ✅ 推荐：点击"登录"按钮
- ✅ 推荐：点击用户名输入框
- ❌ 避免：点击那个按钮
- ❌ 避免：点击左边的东西

### 输入操作
- ✅ 推荐：在用户名输入框中输入"test_user"
- ✅ 推荐：输入密码"123456"
- ❌ 避免：输入一些文字
- ❌ 避免：填写信息

### 验证操作
- ✅ 推荐：验证页面显示"登录成功"文字
- ✅ 推荐：验证用户头像元素存在
- ❌ 避免：检查是否正确
- ❌ 避免：看看对不对

### 等待操作
- ✅ 推荐：等待页面加载完成
- ✅ 推荐：等待"加载中"提示消失
- ❌ 避免：等一会儿
- ❌ 避免：稍等

## 3. 元素定位建议

### 优先级顺序
1. 文本内容（最稳定）
2. resource-id（推荐）
3. content-desc（可用）
4. class-name（不推荐）
5. 坐标位置（最后选择）

### 描述规范
- 使用具体的文本内容：如"登录"、"确定"、"取消"
- 包含控件类型：如"登录按钮"、"用户名输入框"
- 避免模糊描述：如"那个控件"、"这里"

## 4. 常见问题和解决方案

### 问题1：元素定位不准确
解决方案：
- 提供更具体的元素描述
- 包含元素的上下文信息
- 使用多种定位方式组合

### 问题2：操作步骤不清晰
解决方案：
- 将复杂操作拆分为简单步骤
- 每个步骤只包含一个操作
- 使用动词开头描述操作

### 问题3：验证点不明确
解决方案：
- 明确指定要验证的内容
- 包含预期的具体值
- 区分功能验证和UI验证
```

---

*本技术落地方案基于现有UI自动化测试系统架构，充分复用现有智能体框架和AI模型能力，通过集成uiautomator2、pytest、allure等成熟技术栈，实现了从自然语言测试用例到Android自动化脚本的完整转换流程。方案包含了完整的API接口、实际应用示例、智能元素定位、自适应等待、部署运维、测试策略和最佳实践等全方位内容，为团队提供了高效、智能、可扩展的Android自动化测试解决方案。*
