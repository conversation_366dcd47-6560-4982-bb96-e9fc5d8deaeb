"""
Android设备管理智能体
负责管理Android设备连接、监控设备状态和设备操作
支持真机、模拟器、设备农场等多种设备类型
"""
import json
import uuid
import asyncio
import subprocess
from typing import Dict, List, Any, Optional
from datetime import datetime

from autogen_core import message_handler, type_subscription, MessageContext
from loguru import logger

from app.core.messages.android import AndroidDeviceConnectionMessage
from app.core.agents.base import BaseAgent
from app.core.types import TopicTypes, AgentTypes, AGENT_NAMES, AgentPlatform


@type_subscription(topic_type=TopicTypes.ANDROID_DEVICE_MANAGER.value)
class AndroidDeviceManagerAgent(BaseAgent):
    """Android设备管理智能体，负责设备连接和状态管理"""

    def __init__(self, model_client_instance=None, **kwargs):
        """初始化Android设备管理智能体"""
        super().__init__(
            agent_id=AgentTypes.ANDROID_DEVICE_MANAGER.value,
            agent_name=AGENT_NAMES[AgentTypes.ANDROID_DEVICE_MANAGER.value],
            platform=AgentPlatform.ANDROID,
            model_client_instance=model_client_instance,
            **kwargs
        )
        self.connected_devices: Dict[str, Dict[str, Any]] = {}
        self.device_monitors: Dict[str, bool] = {}

        logger.info(f"Android设备管理智能体初始化完成: {self.agent_name}")

    @message_handler
    async def handle_device_connection(self, message: AndroidDeviceConnectionMessage, ctx: MessageContext) -> None:
        """处理设备连接请求"""
        try:
            monitor_id = self.start_performance_monitoring()
            
            await self.send_response(f"📱 处理设备连接请求: {message.device_id}")
            
            if message.connection_type == "adb":
                result = await self._handle_adb_connection(message)
            elif message.connection_type == "wifi":
                result = await self._handle_wifi_connection(message)
            else:
                result = {
                    "success": False,
                    "error": f"不支持的连接类型: {message.connection_type}"
                }
            
            await self.send_response(
                f"✅ 设备连接处理完成: {'成功' if result['success'] else '失败'}",
                is_final=True,
                result={
                    "device_id": message.device_id,
                    "connection_result": result,
                    "metrics": self.end_performance_monitoring(monitor_id)
                }
            )

        except Exception as e:
            await self.handle_exception("handle_device_connection", e)

    async def _handle_adb_connection(self, message: AndroidDeviceConnectionMessage) -> Dict[str, Any]:
        """处理ADB连接"""
        try:
            device_id = message.device_id
            
            # 检查设备是否已连接
            if device_id in self.connected_devices:
                return {
                    "success": True,
                    "message": "设备已连接",
                    "device_info": self.connected_devices[device_id]
                }
            
            # 尝试连接设备
            device_info = await self._connect_adb_device(device_id)
            
            if device_info:
                self.connected_devices[device_id] = device_info
                # 启动设备监控
                await self._start_device_monitoring(device_id)
                
                return {
                    "success": True,
                    "message": "设备连接成功",
                    "device_info": device_info
                }
            else:
                return {
                    "success": False,
                    "error": "设备连接失败"
                }
                
        except Exception as e:
            logger.error(f"ADB连接失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _connect_adb_device(self, device_id: str) -> Optional[Dict[str, Any]]:
        """连接ADB设备"""
        try:
            # 检查设备状态
            cmd = ["adb", "-s", device_id, "get-state"] if device_id else ["adb", "get-state"]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0 or b"device" not in stdout:
                logger.error(f"设备{device_id}未连接或状态异常")
                return None
            
            # 获取设备信息
            device_info = await self._get_device_info(device_id)
            device_info["connection_type"] = "adb"
            device_info["connected_at"] = datetime.now().isoformat()
            
            return device_info
            
        except Exception as e:
            logger.error(f"连接ADB设备失败: {str(e)}")
            return None

    async def _get_device_info(self, device_id: str) -> Dict[str, Any]:
        """获取设备详细信息"""
        try:
            device_info = {
                "device_id": device_id,
                "status": "connected"
            }
            
            # 获取设备属性
            properties = [
                ("brand", "ro.product.brand"),
                ("model", "ro.product.model"),
                ("version", "ro.build.version.release"),
                ("sdk", "ro.build.version.sdk"),
                ("abi", "ro.product.cpu.abi"),
                ("density", "ro.sf.lcd_density"),
                ("resolution", "wm size")
            ]
            
            for prop_name, prop_key in properties:
                if prop_key.startswith("wm"):
                    # 特殊处理分辨率
                    cmd = ["adb", "-s", device_id, "shell", prop_key]
                else:
                    cmd = ["adb", "-s", device_id, "shell", "getprop", prop_key]
                
                try:
                    process = await asyncio.create_subprocess_exec(
                        *cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    
                    stdout, _ = await process.communicate()
                    
                    if process.returncode == 0:
                        value = stdout.decode().strip()
                        if prop_name == "resolution" and "Physical size:" in value:
                            # 解析分辨率
                            resolution_line = [line for line in value.split('\n') if 'Physical size:' in line]
                            if resolution_line:
                                resolution = resolution_line[0].split(':')[1].strip()
                                device_info[prop_name] = resolution
                        else:
                            device_info[prop_name] = value
                            
                except Exception as e:
                    logger.warning(f"获取设备属性{prop_name}失败: {str(e)}")
                    device_info[prop_name] = "unknown"
            
            return device_info
            
        except Exception as e:
            logger.error(f"获取设备信息失败: {str(e)}")
            return {"device_id": device_id, "status": "error", "error": str(e)}

    async def _handle_wifi_connection(self, message: AndroidDeviceConnectionMessage) -> Dict[str, Any]:
        """处理WiFi连接"""
        try:
            device_ip = message.device_config.get("ip_address")
            port = message.device_config.get("port", 5555)
            
            if not device_ip:
                return {
                    "success": False,
                    "error": "WiFi连接需要提供IP地址"
                }
            
            # 连接WiFi设备
            cmd = ["adb", "connect", f"{device_ip}:{port}"]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and b"connected" in stdout:
                device_id = f"{device_ip}:{port}"
                device_info = await self._get_device_info(device_id)
                device_info["connection_type"] = "wifi"
                device_info["ip_address"] = device_ip
                device_info["port"] = port
                
                self.connected_devices[device_id] = device_info
                await self._start_device_monitoring(device_id)
                
                return {
                    "success": True,
                    "message": "WiFi设备连接成功",
                    "device_info": device_info
                }
            else:
                return {
                    "success": False,
                    "error": stderr.decode() if stderr else "WiFi连接失败"
                }
                
        except Exception as e:
            logger.error(f"WiFi连接失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _start_device_monitoring(self, device_id: str):
        """启动设备监控"""
        try:
            if device_id not in self.device_monitors:
                self.device_monitors[device_id] = True
                # 启动后台监控任务
                asyncio.create_task(self._monitor_device(device_id))
                logger.info(f"启动设备监控: {device_id}")
                
        except Exception as e:
            logger.error(f"启动设备监控失败: {str(e)}")

    async def _monitor_device(self, device_id: str):
        """监控设备状态"""
        try:
            while self.device_monitors.get(device_id, False):
                # 检查设备连接状态
                cmd = ["adb", "-s", device_id, "get-state"]
                
                try:
                    process = await asyncio.create_subprocess_exec(
                        *cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    
                    stdout, stderr = await process.communicate()
                    
                    if process.returncode == 0 and b"device" in stdout:
                        # 设备正常连接
                        if device_id in self.connected_devices:
                            self.connected_devices[device_id]["last_check"] = datetime.now().isoformat()
                            self.connected_devices[device_id]["status"] = "connected"
                    else:
                        # 设备连接异常
                        if device_id in self.connected_devices:
                            self.connected_devices[device_id]["status"] = "disconnected"
                            self.connected_devices[device_id]["last_error"] = stderr.decode() if stderr else "连接丢失"
                        
                        logger.warning(f"设备{device_id}连接异常")
                        
                except Exception as e:
                    logger.error(f"监控设备{device_id}时出错: {str(e)}")
                
                # 等待30秒后再次检查
                await asyncio.sleep(30)
                
        except Exception as e:
            logger.error(f"设备监控任务异常: {str(e)}")
        finally:
            if device_id in self.device_monitors:
                del self.device_monitors[device_id]

    async def list_connected_devices(self) -> List[Dict[str, Any]]:
        """列出所有已连接的设备"""
        return list(self.connected_devices.values())

    async def disconnect_device(self, device_id: str) -> bool:
        """断开设备连接"""
        try:
            # 停止监控
            if device_id in self.device_monitors:
                self.device_monitors[device_id] = False
            
            # 从连接列表中移除
            if device_id in self.connected_devices:
                device_info = self.connected_devices[device_id]
                
                # 如果是WiFi连接，执行adb disconnect
                if device_info.get("connection_type") == "wifi":
                    cmd = ["adb", "disconnect", device_id]
                    process = await asyncio.create_subprocess_exec(
                        *cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    await process.communicate()
                
                del self.connected_devices[device_id]
                logger.info(f"设备{device_id}已断开连接")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"断开设备连接失败: {str(e)}")
            return False

    async def get_device_status(self, device_id: str) -> Optional[Dict[str, Any]]:
        """获取设备状态"""
        return self.connected_devices.get(device_id)

    async def scan_devices(self) -> List[Dict[str, Any]]:
        """扫描可用设备"""
        try:
            cmd = ["adb", "devices", "-l"]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"扫描设备失败: {stderr.decode()}")
                return []
            
            devices = []
            lines = stdout.decode().strip().split('\n')[1:]  # 跳过标题行
            
            for line in lines:
                if line.strip() and '\tdevice' in line:
                    parts = line.split('\t')
                    device_id = parts[0]
                    
                    # 解析设备信息
                    device_info = {
                        "device_id": device_id,
                        "status": "available",
                        "connection_type": "usb" if ":" not in device_id else "wifi"
                    }
                    
                    # 解析额外信息
                    if len(parts) > 2:
                        extra_info = parts[2]
                        for item in extra_info.split():
                            if ':' in item:
                                key, value = item.split(':', 1)
                                device_info[key] = value
                    
                    devices.append(device_info)
            
            return devices
            
        except Exception as e:
            logger.error(f"扫描设备失败: {str(e)}")
            return []

    async def install_app(self, device_id: str, apk_path: str) -> Dict[str, Any]:
        """在设备上安装应用"""
        try:
            cmd = ["adb", "-s", device_id, "install", "-r", apk_path]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            success = process.returncode == 0 and b"Success" in stdout
            
            return {
                "success": success,
                "output": stdout.decode() if stdout else "",
                "error": stderr.decode() if stderr else ""
            }
            
        except Exception as e:
            logger.error(f"安装应用失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def uninstall_app(self, device_id: str, package_name: str) -> Dict[str, Any]:
        """卸载设备上的应用"""
        try:
            cmd = ["adb", "-s", device_id, "uninstall", package_name]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            success = process.returncode == 0 and b"Success" in stdout
            
            return {
                "success": success,
                "output": stdout.decode() if stdout else "",
                "error": stderr.decode() if stderr else ""
            }
            
        except Exception as e:
            logger.error(f"卸载应用失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
