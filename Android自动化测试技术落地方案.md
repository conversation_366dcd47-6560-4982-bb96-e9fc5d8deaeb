# Android自动化测试技术落地方案

## 📋 方案概述

基于现有UI自动化测试系统架构，集成Midscene.js Android SDK，实现Android移动端自动化测试能力。本方案将在现有多模态AI分析和智能体协作基础上，扩展支持Android设备的UI自动化测试。

### 🎯 核心目标
- **扩展平台支持**: 在现有Web自动化基础上增加Android自动化能力
- **统一技术栈**: 复用现有智能体架构和AI模型能力
- **无缝集成**: 与现有系统架构保持一致的用户体验
- **多设备支持**: 支持真机、模拟器和云设备的自动化测试

## 🏗️ 技术架构设计

### 整体架构扩展

```
┌─────────────────────────────────────────────────────────────────┐
│                UI自动化测试系统 - Android扩展架构                │
├─────────────────────────────────────────────────────────────────┤
│  🌐 前端层 (React + TypeScript)                                │
│  ├── 仪表盘 (Dashboard)                                        │
│  ├── Web测试模块 (Web Testing)                                 │
│  ├── Android测试模块 (Android Testing) ⭐新增                   │
│  │   ├── 设备管理 (Device Management)                          │
│  │   ├── 应用测试 (App Testing)                                │
│  │   └── 测试执行 (Test Execution)                             │
│  └── 测试结果 (Test Results)                                   │
├─────────────────────────────────────────────────────────────────┤
│  🔌 API网关层 (FastAPI)                                        │
│  ├── Web图片分析接口 (/web/*)                                  │
│  ├── Android分析接口 (/android/*) ⭐新增                       │
│  │   ├── /android/devices - 设备管理                           │
│  │   ├── /android/analyze - 界面分析                           │
│  │   ├── /android/scripts - 脚本管理                           │
│  │   └── /android/execution - 脚本执行                         │
│  └── 统一报告接口 (/reports/*)                                 │
├─────────────────────────────────────────────────────────────────┤
│  🔧 业务服务层                                                 │
│  ├── Web编排服务 (WebOrchestratorService)                     │
│  ├── Android编排服务 (AndroidOrchestratorService) ⭐新增       │
│  ├── 设备管理服务 (DeviceManagementService) ⭐新增             │
│  └── 统一执行服务 (UnifiedExecutionService)                   │
├─────────────────────────────────────────────────────────────────┤
│  🤖 智能体层 (AutoGen Framework)                               │
│  ├── Web智能体组 (Web Agents)                                  │
│  └── Android智能体组 (Android Agents) ⭐新增                   │
│      ├── Android界面分析智能体 (AndroidAnalyzerAgent)          │
│      ├── Android脚本生成智能体 (AndroidScriptGeneratorAgent)   │
│      ├── Android脚本执行智能体 (AndroidExecutorAgent)          │
│      └── 设备管理智能体 (DeviceManagerAgent)                   │
├─────────────────────────────────────────────────────────────────┤
│  💾 数据存储层                                                 │
│  ├── MySQL - 项目、会话、脚本、执行记录、设备信息              │
│  ├── Neo4j - 脚本关系、设备拓扑                                │
│  └── Milvus - 语义搜索、相似性匹配                             │
├─────────────────────────────────────────────────────────────────┤
│  🧠 AI模型层                                                   │
│  ├── DeepSeek-Chat (文本生成)                                  │
│  ├── Qwen-VL-Max (多模态分析)                                  │
│  └── UI-TARS (GUI专用模型) - 支持Android界面识别               │
├─────────────────────────────────────────────────────────────────┤
│  🔌 集成层                                                     │
│  ├── MidScene.js Web (浏览器自动化)                            │
│  ├── MidScene.js Android (Android自动化) ⭐新增                │
│  ├── ADB (Android Debug Bridge) ⭐新增                         │
│  └── Appium (备用Android自动化方案) ⭐新增                      │
└─────────────────────────────────────────────────────────────────┘
```

### Android模块核心组件

#### 1. 前端Android模块
```
frontend/src/pages/Android/
├── DeviceManagement/     # 设备管理页面
│   ├── DeviceList.tsx    # 设备列表
│   ├── DeviceDetail.tsx  # 设备详情
│   └── DeviceMonitor.tsx # 设备监控
├── TestCreation/         # 测试创建页面
│   ├── AppAnalysis.tsx   # 应用界面分析
│   ├── ScriptGeneration.tsx # 脚本生成
│   └── TestConfiguration.tsx # 测试配置
├── TestExecution/        # 测试执行页面
│   ├── ExecutionDashboard.tsx # 执行仪表盘
│   ├── RealTimeMonitor.tsx # 实时监控
│   └── BatchExecution.tsx # 批量执行
└── TestResults/          # 测试结果页面
    ├── AndroidReports.tsx # Android测试报告
    ├── PerformanceMetrics.tsx # 性能指标
    └── ScreenshotGallery.tsx # 截图画廊
```

#### 2. 后端Android模块
```
backend/app/
├── api/v1/endpoints/android/  # Android API端点
│   ├── devices.py             # 设备管理API
│   ├── analysis.py            # 界面分析API
│   ├── scripts.py             # 脚本管理API
│   └── execution.py           # 执行管理API
├── agents/android/            # Android智能体
│   ├── analyzer_agent.py      # 界面分析智能体
│   ├── script_generator_agent.py # 脚本生成智能体
│   ├── executor_agent.py      # 执行智能体
│   └── device_manager_agent.py # 设备管理智能体
├── services/android/          # Android服务层
│   ├── orchestrator_service.py # 编排服务
│   ├── device_service.py      # 设备服务
│   ├── analysis_service.py    # 分析服务
│   └── execution_service.py   # 执行服务
├── database/models/android/   # Android数据模型
│   ├── device.py              # 设备模型
│   ├── android_script.py      # Android脚本模型
│   └── android_execution.py   # Android执行模型
└── core/messages/android.py   # Android消息类型
```

## 🔄 核心业务流程

### 1. Android设备管理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关
    participant DM as 设备管理服务
    participant ADB as ADB服务

    U->>F: 扫描设备
    F->>A: GET /android/devices/scan
    A->>DM: 扫描连接设备
    DM->>ADB: adb devices -l
    ADB->>DM: 返回设备列表
    DM->>A: 返回设备信息
    A->>F: 设备列表
    F->>U: 显示可用设备
```

### 2. Android界面分析流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关
    participant O as Android编排服务
    participant AA as Android分析智能体
    participant AI as AI模型

    U->>F: 上传Android截图/选择设备
    F->>A: POST /android/analyze/screenshot
    A->>O: 创建分析会话
    O->>AA: 发送界面分析请求
    
    AA->>AI: 多模态AI分析Android界面
    AI->>AA: 返回UI元素识别结果
    AA->>O: 返回分析结果
    
    O->>A: 返回完整分析结果
    A->>F: SSE实时推送
    F->>U: 显示Android界面分析结果
```

### 3. Android脚本执行流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关
    participant ES as 执行服务
    participant AE as Android执行智能体
    participant M as MidScene Android
    participant ADB as ADB

    U->>F: 选择Android脚本执行
    F->>A: POST /android/execution/execute
    A->>ES: 创建执行会话
    ES->>AE: 发送执行请求
    
    AE->>M: 调用MidScene Android SDK
    M->>ADB: 通过ADB控制设备
    ADB->>ADB: 执行自动化操作
    ADB->>M: 返回执行结果
    M->>AE: 返回测试报告
    
    AE->>ES: 更新执行状态
    ES->>A: 返回执行结果
    A->>F: SSE实时推送状态
    F->>U: 显示执行进度和结果
```

## 💾 数据库设计扩展

### Android相关表结构

#### 1. 设备管理表
```sql
-- Android设备表
CREATE TABLE android_devices (
    id VARCHAR(36) PRIMARY KEY,
    device_id VARCHAR(100) NOT NULL UNIQUE,
    device_name VARCHAR(200),
    platform_version VARCHAR(50),
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    status ENUM('connected', 'disconnected', 'busy', 'error') DEFAULT 'disconnected',
    capabilities JSON,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 设备会话表
CREATE TABLE android_device_sessions (
    id VARCHAR(36) PRIMARY KEY,
    device_id VARCHAR(36) NOT NULL,
    session_type ENUM('analysis', 'execution', 'monitoring') NOT NULL,
    status ENUM('active', 'completed', 'failed', 'cancelled') DEFAULT 'active',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP NULL,
    metadata JSON,
    FOREIGN KEY (device_id) REFERENCES android_devices(id)
);
```

#### 2. Android脚本表
```sql
-- Android测试脚本表
CREATE TABLE android_test_scripts (
    id VARCHAR(36) PRIMARY KEY,
    project_id VARCHAR(36) NOT NULL,
    session_id VARCHAR(36),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    script_type ENUM('midscene', 'appium', 'uiautomator') DEFAULT 'midscene',
    script_content LONGTEXT NOT NULL,
    target_app_package VARCHAR(200),
    target_app_activity VARCHAR(200),
    device_requirements JSON,
    estimated_duration INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (session_id) REFERENCES sessions(id)
);
```

#### 3. Android执行记录表
```sql
-- Android脚本执行记录表
CREATE TABLE android_script_executions (
    id VARCHAR(36) PRIMARY KEY,
    script_id VARCHAR(36) NOT NULL,
    device_id VARCHAR(36) NOT NULL,
    execution_type ENUM('single', 'batch', 'scheduled') DEFAULT 'single',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP NULL,
    duration INT DEFAULT 0,
    result_summary JSON,
    error_message TEXT,
    screenshots_path VARCHAR(500),
    video_path VARCHAR(500),
    logs_path VARCHAR(500),
    performance_metrics JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (script_id) REFERENCES android_test_scripts(id),
    FOREIGN KEY (device_id) REFERENCES android_devices(id)
);
```

## 🤖 Android智能体系统

### 智能体架构设计

#### 1. Android界面分析智能体 (AndroidAnalyzerAgent)
- **功能**: 分析Android应用界面，识别UI元素和交互流程
- **输入**: Android截图 + UI层次结构XML + 测试描述
- **输出**: Android界面分析结果 + UI元素列表 + 测试建议
- **AI模型**: UI-TARS（推荐）或 Qwen-VL-Max

#### 2. Android脚本生成智能体 (AndroidScriptGeneratorAgent)
- **功能**: 基于界面分析结果生成Android自动化脚本
- **输入**: Android界面分析结果 + 测试需求
- **输出**: MidScene Android脚本 或 Appium脚本
- **AI模型**: DeepSeek-Chat

#### 3. Android执行智能体 (AndroidExecutorAgent)
- **功能**: 执行Android自动化脚本，管理设备连接
- **输入**: Android脚本 + 设备信息 + 执行配置
- **输出**: 执行结果 + 测试报告 + 截图/视频
- **依赖**: MidScene Android SDK + ADB

#### 4. 设备管理智能体 (DeviceManagerAgent)
- **功能**: 管理Android设备连接、监控设备状态
- **输入**: 设备扫描请求 + 设备操作指令
- **输出**: 设备状态信息 + 操作结果
- **依赖**: ADB + 设备驱动

### 智能体协作流程

```mermaid
graph TD
    A[用户请求] --> B[Android编排服务]
    B --> C[设备管理智能体]
    C --> D[Android分析智能体]
    D --> E[Android脚本生成智能体]
    E --> F[Android执行智能体]
    F --> G[结果收集和报告]
    
    C --> H[设备状态监控]
    D --> I[界面元素识别]
    E --> J[脚本优化]
    F --> K[执行监控]
```

## 🔧 技术实现方案

### 1. 环境准备和依赖安装

#### 系统要求
- Node.js 18+ 
- Python 3.9+
- Android SDK (ADB)
- 已连接的Android设备或模拟器

#### 核心依赖
```json
{
  "dependencies": {
    "@midscene/android": "latest",
    "adbkit": "^2.11.3",
    "appium": "^2.0.0",
    "appium-uiautomator2-driver": "^2.0.0"
  }
}
```

### 2. 配置管理扩展

#### 新增Android配置类
```python
class AndroidSettings(BaseSettings):
    """Android自动化配置"""
    
    # ADB配置
    ADB_PATH: str = "adb"
    ADB_TIMEOUT: int = 30
    ADB_REMOTE_HOST: Optional[str] = None
    ADB_REMOTE_PORT: Optional[int] = None
    
    # MidScene Android配置
    MIDSCENE_ANDROID_TIMEOUT: int = 300
    AUTO_DISMISS_KEYBOARD: bool = True
    IME_STRATEGY: str = "always-yadb"
    
    # 设备管理配置
    DEVICE_SCAN_INTERVAL: int = 30
    MAX_CONCURRENT_DEVICES: int = 5
    DEVICE_HEALTH_CHECK_INTERVAL: int = 60
    
    # Appium配置（备用方案）
    APPIUM_SERVER_URL: str = "http://localhost:4723"
    APPIUM_TIMEOUT: int = 60
```

### 3. API端点实现

#### 设备管理API
```python
@router.get("/devices/scan")
async def scan_devices():
    """扫描连接的Android设备"""
    devices = await device_service.scan_connected_devices()
    return {"devices": devices}

@router.get("/devices/{device_id}/info")
async def get_device_info(device_id: str):
    """获取设备详细信息"""
    device_info = await device_service.get_device_info(device_id)
    return device_info

@router.post("/devices/{device_id}/screenshot")
async def capture_screenshot(device_id: str):
    """捕获设备截图"""
    screenshot = await device_service.capture_screenshot(device_id)
    return {"screenshot": screenshot}
```

#### Android分析API
```python
@router.post("/analyze/screenshot")
async def analyze_android_screenshot(request: AndroidAnalysisRequest):
    """分析Android界面截图"""
    result = await android_orchestrator.analyze_screenshot(
        screenshot_data=request.screenshot_data,
        ui_hierarchy=request.ui_hierarchy,
        test_description=request.test_description,
        device_info=request.device_info
    )
    return result
```

## 📊 监控和运维

### 性能监控指标
- **设备连接状态**: 实时监控设备连接健康度
- **脚本执行性能**: 监控Android脚本执行时间和成功率
- **设备资源使用**: 监控设备CPU、内存、电量使用情况
- **ADB连接稳定性**: 监控ADB连接质量和重连次数

### 错误处理策略
- **设备断连重试**: 自动重连断开的设备
- **脚本执行失败恢复**: 智能重试和错误诊断
- **资源清理**: 自动清理测试产生的临时文件
- **设备状态恢复**: 测试后恢复设备到初始状态

## 🚀 部署方案

### 开发环境部署
1. **安装Android SDK和ADB**
2. **配置设备连接**（USB调试或网络ADB）
3. **安装MidScene Android依赖**
4. **配置AI模型API密钥**
5. **启动开发服务器**

### 生产环境部署
1. **容器化部署**：Docker容器包含Android SDK
2. **设备农场集成**：支持云设备和设备农场
3. **负载均衡**：多设备并发执行负载分配
4. **监控告警**：设备状态和执行结果监控

## 📈 实施路线图

### 第一阶段：基础设施搭建（2周）
- [ ] Android模块前端页面开发
- [ ] Android API端点实现
- [ ] 设备管理服务开发
- [ ] 数据库表结构创建

### 第二阶段：核心功能实现（3周）
- [ ] Android智能体开发
- [ ] MidScene Android SDK集成
- [ ] 界面分析功能实现
- [ ] 脚本生成功能实现

### 第三阶段：执行引擎开发（2周）
- [ ] Android脚本执行引擎
- [ ] 实时监控和日志收集
- [ ] 测试报告生成
- [ ] 错误处理和重试机制

### 第四阶段：优化和扩展（2周）
- [ ] 性能优化和并发支持
- [ ] 批量执行功能
- [ ] 设备农场集成
- [ ] 监控告警系统

### 第五阶段：测试和发布（1周）
- [ ] 端到端测试
- [ ] 性能测试
- [ ] 文档完善
- [ ] 正式发布

## 💻 核心代码实现示例

### 1. Android智能体实现

#### AndroidAnalyzerAgent 实现示例
```python
"""
Android界面分析智能体
基于UI-TARS模型分析Android应用界面
"""
import base64
from typing import Dict, List, Any, Optional
from autogen_core import message_handler, type_subscription, MessageContext
from loguru import logger

from app.core.messages.android import AndroidAnalysisRequest, AndroidAnalysisResult
from app.core.agents.base import BaseAgent
from app.core.types import TopicTypes, AgentTypes

@type_subscription(topic_type=TopicTypes.ANDROID_ANALYSIS.value)
class AndroidAnalyzerAgent(BaseAgent):
    """Android界面分析智能体"""

    def __init__(self, model_client, **kwargs):
        super().__init__(
            name=AgentTypes.ANDROID_ANALYZER.value,
            model_client=model_client,
            **kwargs
        )
        self.system_message = self._get_system_message()

    def _get_system_message(self) -> str:
        return """你是一个专业的Android界面分析专家，具备以下能力：

1. **UI元素识别**: 准确识别Android界面中的各种UI元素
   - 按钮(Button)、文本框(EditText)、文本(TextView)
   - 列表(ListView/RecyclerView)、图片(ImageView)
   - 菜单(Menu)、对话框(Dialog)、底部导航等

2. **交互流程分析**: 理解用户交互流程和应用逻辑
   - 页面跳转关系
   - 用户操作序列
   - 数据流转过程

3. **测试场景生成**: 基于界面分析生成测试场景
   - 功能测试场景
   - 异常处理场景
   - 用户体验测试场景

4. **Android特性理解**: 深度理解Android平台特性
   - Activity生命周期
   - Fragment管理
   - Intent机制
   - 权限系统

请以JSON格式返回分析结果，包含UI元素列表、测试场景和操作步骤。"""

    @message_handler
    async def handle_analysis_request(
        self,
        message: AndroidAnalysisRequest,
        ctx: MessageContext
    ) -> AndroidAnalysisResult:
        """处理Android界面分析请求"""
        try:
            logger.info(f"开始分析Android界面，会话ID: {message.session_id}")

            # 构建分析提示词
            analysis_prompt = self._build_analysis_prompt(message)

            # 调用AI模型进行分析
            response = await self._call_multimodal_model(
                prompt=analysis_prompt,
                image_data=message.screenshot_data,
                ui_hierarchy=message.ui_hierarchy
            )

            # 解析AI响应
            analysis_result = self._parse_analysis_response(response)

            logger.info(f"Android界面分析完成，识别到 {len(analysis_result.ui_elements)} 个UI元素")

            return analysis_result

        except Exception as e:
            logger.error(f"Android界面分析失败: {str(e)}")
            raise

    def _build_analysis_prompt(self, message: AndroidAnalysisRequest) -> str:
        """构建分析提示词"""
        prompt = f"""
请分析这个Android应用界面截图，测试需求：{message.test_description}

分析要求：
1. 识别所有可见的UI元素，包括类型、位置、文本内容
2. 分析用户可能的交互操作
3. 生成相应的测试场景和步骤
4. 考虑Android平台的特殊性（如返回键、菜单键等）

"""

        if message.ui_hierarchy:
            prompt += f"\nUI层次结构信息：\n{message.ui_hierarchy}\n"

        if message.device_info:
            prompt += f"\n设备信息：{message.device_info}\n"

        if message.app_package:
            prompt += f"\n应用包名：{message.app_package}\n"

        return prompt

    async def _call_multimodal_model(
        self,
        prompt: str,
        image_data: str,
        ui_hierarchy: Optional[str] = None
    ) -> str:
        """调用多模态AI模型"""
        # 这里调用配置的多模态模型（UI-TARS或Qwen-VL）
        # 具体实现依赖于模型客户端
        messages = [
            {"role": "system", "content": self.system_message},
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": image_data}}
                ]
            }
        ]

        response = await self.model_client.create_completion(messages=messages)
        return response.choices[0].message.content
```

#### AndroidScriptGeneratorAgent 实现示例
```python
"""
Android脚本生成智能体
基于界面分析结果生成MidScene Android自动化脚本
"""
from typing import Dict, List, Any
from autogen_core import message_handler, type_subscription, MessageContext

from app.core.messages.android import AndroidScriptGenerationRequest, AndroidGeneratedScript
from app.core.agents.base import BaseAgent

@type_subscription(topic_type=TopicTypes.ANDROID_SCRIPT_GENERATION.value)
class AndroidScriptGeneratorAgent(BaseAgent):
    """Android脚本生成智能体"""

    def __init__(self, model_client, **kwargs):
        super().__init__(
            name=AgentTypes.ANDROID_SCRIPT_GENERATOR.value,
            model_client=model_client,
            **kwargs
        )
        self.system_message = self._get_system_message()

    def _get_system_message(self) -> str:
        return """你是一个专业的Android自动化测试脚本生成专家，精通MidScene.js Android SDK。

核心能力：
1. **MidScene Android脚本生成**: 基于界面分析生成高质量的自动化脚本
2. **最佳实践应用**: 遵循Android自动化测试最佳实践
3. **错误处理**: 包含完善的异常处理和重试机制
4. **性能优化**: 生成高效、稳定的测试脚本

脚本格式要求：
- 使用MidScene Android SDK语法
- 包含详细的注释说明
- 添加适当的等待和验证
- 处理Android特有的交互（如权限弹窗、键盘等）

请生成完整可执行的TypeScript/JavaScript代码。"""

    @message_handler
    async def handle_script_generation(
        self,
        message: AndroidScriptGenerationRequest,
        ctx: MessageContext
    ) -> AndroidGeneratedScript:
        """处理Android脚本生成请求"""
        try:
            logger.info(f"开始生成Android脚本，会话ID: {message.session_id}")

            # 构建脚本生成提示词
            generation_prompt = self._build_generation_prompt(message)

            # 调用AI模型生成脚本
            script_content = await self._generate_script(generation_prompt)

            # 生成完整的项目结构
            project_files = self._generate_project_files(script_content, message)

            logger.info("Android脚本生成完成")

            return AndroidGeneratedScript(
                format="midscene-android",
                content=script_content,
                project_files=project_files,
                estimated_duration=self._estimate_duration(script_content),
                framework="midscene"
            )

        except Exception as e:
            logger.error(f"Android脚本生成失败: {str(e)}")
            raise

    def _build_generation_prompt(self, message: AndroidScriptGenerationRequest) -> str:
        """构建脚本生成提示词"""
        return f"""
基于以下Android界面分析结果，生成MidScene Android自动化测试脚本：

界面分析结果：
{message.analysis_result}

测试需求：
{message.test_description}

脚本要求：
1. 使用MidScene Android SDK
2. 包含完整的设备连接和初始化
3. 添加适当的等待和验证
4. 处理可能的权限弹窗和异常情况
5. 生成清晰的测试报告

请生成完整的TypeScript代码，包含：
- 设备连接和初始化
- 具体的测试步骤
- 断言和验证
- 错误处理
- 资源清理
"""

    def _generate_project_files(self, script_content: str, message: AndroidScriptGenerationRequest) -> Dict[str, str]:
        """生成完整的项目文件"""
        return {
            "package.json": self._get_package_json(),
            "tsconfig.json": self._get_tsconfig(),
            "test.spec.ts": script_content,
            "README.md": self._get_readme(message.test_description),
            ".env.example": self._get_env_example()
        }

    def _get_package_json(self) -> str:
        """生成package.json"""
        return """{
  "name": "midscene-android-test",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "test": "tsx test.spec.ts",
    "test:debug": "tsx --inspect test.spec.ts"
  },
  "devDependencies": {
    "@midscene/android": "latest",
    "tsx": "^4.0.0",
    "typescript": "^5.0.0"
  }
}"""

    def _get_tsconfig(self) -> str:
        """生成tsconfig.json"""
        return """{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  }
}"""
```

### 2. 设备管理服务实现

#### DeviceManagementService 实现示例
```python
"""
Android设备管理服务
负责设备发现、连接管理、状态监控
"""
import asyncio
import subprocess
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger

from app.core.config import get_settings
from app.database.models.android.device import AndroidDevice

class DeviceManagementService:
    """Android设备管理服务"""

    def __init__(self):
        self.settings = get_settings()
        self.connected_devices: Dict[str, AndroidDevice] = {}
        self.monitoring_tasks: Dict[str, asyncio.Task] = {}

    async def scan_connected_devices(self) -> List[Dict[str, Any]]:
        """扫描连接的Android设备"""
        try:
            # 执行adb devices命令
            result = await self._run_adb_command(["devices", "-l"])
            devices = self._parse_device_list(result)

            # 更新设备状态
            await self._update_device_status(devices)

            logger.info(f"扫描到 {len(devices)} 个Android设备")
            return devices

        except Exception as e:
            logger.error(f"设备扫描失败: {str(e)}")
            raise

    async def get_device_info(self, device_id: str) -> Dict[str, Any]:
        """获取设备详细信息"""
        try:
            # 获取设备属性
            props = await self._get_device_properties(device_id)

            # 获取设备状态
            status = await self._check_device_status(device_id)

            # 获取应用列表
            apps = await self._get_installed_apps(device_id)

            return {
                "device_id": device_id,
                "properties": props,
                "status": status,
                "installed_apps": apps,
                "last_updated": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取设备信息失败 {device_id}: {str(e)}")
            raise

    async def capture_screenshot(self, device_id: str) -> str:
        """捕获设备截图"""
        try:
            # 执行截图命令
            result = await self._run_adb_command([
                "-s", device_id, "exec-out", "screencap", "-p"
            ], return_bytes=True)

            # 转换为base64
            import base64
            screenshot_b64 = base64.b64encode(result).decode('utf-8')

            logger.info(f"设备 {device_id} 截图捕获成功")
            return f"data:image/png;base64,{screenshot_b64}"

        except Exception as e:
            logger.error(f"截图捕获失败 {device_id}: {str(e)}")
            raise

    async def get_ui_hierarchy(self, device_id: str) -> str:
        """获取UI层次结构"""
        try:
            # 执行UI dump命令
            result = await self._run_adb_command([
                "-s", device_id, "exec-out", "uiautomator", "dump", "/dev/stdout"
            ])

            logger.info(f"设备 {device_id} UI层次结构获取成功")
            return result

        except Exception as e:
            logger.error(f"UI层次结构获取失败 {device_id}: {str(e)}")
            raise

    async def start_device_monitoring(self, device_id: str):
        """开始设备监控"""
        if device_id in self.monitoring_tasks:
            return

        task = asyncio.create_task(self._monitor_device(device_id))
        self.monitoring_tasks[device_id] = task
        logger.info(f"开始监控设备: {device_id}")

    async def stop_device_monitoring(self, device_id: str):
        """停止设备监控"""
        if device_id in self.monitoring_tasks:
            self.monitoring_tasks[device_id].cancel()
            del self.monitoring_tasks[device_id]
            logger.info(f"停止监控设备: {device_id}")

    async def _run_adb_command(
        self,
        args: List[str],
        return_bytes: bool = False
    ) -> str:
        """执行ADB命令"""
        cmd = [self.settings.ADB_PATH] + args

        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        stdout, stderr = await process.communicate()

        if process.returncode != 0:
            raise Exception(f"ADB命令执行失败: {stderr.decode()}")

        if return_bytes:
            return stdout
        return stdout.decode()

    def _parse_device_list(self, output: str) -> List[Dict[str, Any]]:
        """解析设备列表输出"""
        devices = []
        lines = output.strip().split('\n')[1:]  # 跳过标题行

        for line in lines:
            if not line.strip():
                continue

            parts = line.split()
            if len(parts) >= 2:
                device_id = parts[0]
                status = parts[1]

                device_info = {
                    "device_id": device_id,
                    "status": status,
                    "properties": {}
                }

                # 解析额外属性
                for part in parts[2:]:
                    if ':' in part:
                        key, value = part.split(':', 1)
                        device_info["properties"][key] = value

                devices.append(device_info)

        return devices

    async def _monitor_device(self, device_id: str):
        """监控设备状态"""
        while True:
            try:
                # 检查设备连接状态
                status = await self._check_device_status(device_id)

                # 更新设备状态到数据库
                await self._update_device_in_db(device_id, status)

                # 等待下次检查
                await asyncio.sleep(self.settings.DEVICE_HEALTH_CHECK_INTERVAL)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"设备监控异常 {device_id}: {str(e)}")
                await asyncio.sleep(10)  # 错误时短暂等待

# 创建全局设备管理服务实例
device_management_service = DeviceManagementService()
```

### 3. Android API端点完整实现

#### Android API路由实现
```python
"""
Android模块API端点
提供设备管理、界面分析、脚本执行等功能
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from typing import List, Dict, Any, Optional
from loguru import logger

from app.core.messages.android import (
    AndroidAnalysisRequest,
    AndroidScriptGenerationRequest,
    AndroidExecutionRequest
)
from app.services.android.orchestrator_service import AndroidOrchestratorService
from app.services.android.device_service import device_management_service
from app.database.models.android.device import AndroidDevice

router = APIRouter(prefix="/android", tags=["Android自动化"])

# 依赖注入
async def get_android_orchestrator() -> AndroidOrchestratorService:
    """获取Android编排服务"""
    from app.services.android.orchestrator_service import android_orchestrator_service
    return android_orchestrator_service

@router.get("/devices/scan")
async def scan_devices(
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """扫描连接的Android设备"""
    try:
        devices = await device_management_service.scan_connected_devices()

        # 后台启动设备监控
        for device in devices:
            if device["status"] == "device":
                background_tasks.add_task(
                    device_management_service.start_device_monitoring,
                    device["device_id"]
                )

        return {
            "success": True,
            "devices": devices,
            "total": len(devices)
        }

    except Exception as e:
        logger.error(f"设备扫描失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/devices/{device_id}/info")
async def get_device_info(device_id: str) -> Dict[str, Any]:
    """获取设备详细信息"""
    try:
        device_info = await device_management_service.get_device_info(device_id)
        return {
            "success": True,
            "device_info": device_info
        }

    except Exception as e:
        logger.error(f"获取设备信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/devices/{device_id}/screenshot")
async def capture_screenshot(device_id: str) -> Dict[str, Any]:
    """捕获设备截图"""
    try:
        screenshot = await device_management_service.capture_screenshot(device_id)
        ui_hierarchy = await device_management_service.get_ui_hierarchy(device_id)

        return {
            "success": True,
            "screenshot": screenshot,
            "ui_hierarchy": ui_hierarchy,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"截图捕获失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyze/screenshot")
async def analyze_android_screenshot(
    request: AndroidAnalysisRequest,
    orchestrator: AndroidOrchestratorService = Depends(get_android_orchestrator)
) -> Dict[str, Any]:
    """分析Android界面截图"""
    try:
        logger.info(f"开始Android界面分析，会话ID: {request.session_id}")

        # 调用编排服务进行分析
        result = await orchestrator.analyze_screenshot(request)

        return {
            "success": True,
            "session_id": request.session_id,
            "analysis_result": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Android界面分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/scripts/generate")
async def generate_android_script(
    request: AndroidScriptGenerationRequest,
    orchestrator: AndroidOrchestratorService = Depends(get_android_orchestrator)
) -> Dict[str, Any]:
    """生成Android自动化脚本"""
    try:
        logger.info(f"开始生成Android脚本，会话ID: {request.session_id}")

        # 调用编排服务生成脚本
        script = await orchestrator.generate_script(request)

        return {
            "success": True,
            "session_id": request.session_id,
            "generated_script": script,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Android脚本生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/execution/execute")
async def execute_android_script(
    request: AndroidExecutionRequest,
    background_tasks: BackgroundTasks,
    orchestrator: AndroidOrchestratorService = Depends(get_android_orchestrator)
) -> Dict[str, Any]:
    """执行Android自动化脚本"""
    try:
        logger.info(f"开始执行Android脚本，执行ID: {request.execution_id}")

        # 后台执行脚本
        background_tasks.add_task(
            orchestrator.execute_script,
            request
        )

        return {
            "success": True,
            "execution_id": request.execution_id,
            "status": "started",
            "message": "脚本执行已启动，请通过SSE监控执行状态",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Android脚本执行失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/execution/{execution_id}/status")
async def get_execution_status(execution_id: str) -> Dict[str, Any]:
    """获取脚本执行状态"""
    try:
        # 从数据库查询执行状态
        from app.database.models.android.android_execution import AndroidScriptExecution
        execution = await AndroidScriptExecution.get_by_id(execution_id)

        if not execution:
            raise HTTPException(status_code=404, detail="执行记录不存在")

        return {
            "success": True,
            "execution_id": execution_id,
            "status": execution.status,
            "progress": execution.progress,
            "result": execution.result_summary,
            "error": execution.error_message,
            "timestamp": execution.updated_at.isoformat()
        }

    except Exception as e:
        logger.error(f"获取执行状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
```

### 4. 前端Android模块实现

#### Android设备管理页面
```typescript
/**
 * Android设备管理页面
 * 提供设备扫描、连接管理、状态监控功能
 */
import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Tag, Space, message, Modal, Descriptions } from 'antd';
import {
  MobileOutlined,
  ReloadOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  StopOutlined
} from '@ant-design/icons';
import { androidApi } from '@/services/androidApi';

interface AndroidDevice {
  device_id: string;
  status: string;
  properties: Record<string, string>;
  last_seen: string;
}

const AndroidDeviceManagement: React.FC = () => {
  const [devices, setDevices] = useState<AndroidDevice[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<AndroidDevice | null>(null);
  const [deviceDetailVisible, setDeviceDetailVisible] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState<any>(null);

  // 扫描设备
  const scanDevices = async () => {
    setLoading(true);
    try {
      const response = await androidApi.scanDevices();
      if (response.success) {
        setDevices(response.devices);
        message.success(`扫描到 ${response.total} 个设备`);
      }
    } catch (error) {
      message.error('设备扫描失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 查看设备详情
  const viewDeviceDetail = async (device: AndroidDevice) => {
    try {
      const response = await androidApi.getDeviceInfo(device.device_id);
      if (response.success) {
        setSelectedDevice(device);
        setDeviceInfo(response.device_info);
        setDeviceDetailVisible(true);
      }
    } catch (error) {
      message.error('获取设备信息失败');
    }
  };

  // 捕获截图
  const captureScreenshot = async (deviceId: string) => {
    try {
      const response = await androidApi.captureScreenshot(deviceId);
      if (response.success) {
        // 显示截图预览
        Modal.info({
          title: '设备截图',
          width: 800,
          content: (
            <div style={{ textAlign: 'center' }}>
              <img
                src={response.screenshot}
                alt="设备截图"
                style={{ maxWidth: '100%', maxHeight: '600px' }}
              />
            </div>
          ),
        });
      }
    } catch (error) {
      message.error('截图捕获失败');
    }
  };

  // 设备状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      'device': { color: 'green', text: '已连接' },
      'offline': { color: 'red', text: '离线' },
      'unauthorized': { color: 'orange', text: '未授权' },
      'no permissions': { color: 'red', text: '无权限' }
    };

    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: '设备ID',
      dataIndex: 'device_id',
      key: 'device_id',
      render: (text: string) => (
        <Space>
          <MobileOutlined />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '设备型号',
      key: 'model',
      render: (record: AndroidDevice) =>
        record.properties?.model || record.properties?.product || '-',
    },
    {
      title: '最后连接',
      dataIndex: 'last_seen',
      key: 'last_seen',
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: AndroidDevice) => (
        <Space>
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => viewDeviceDetail(record)}
          >
            详情
          </Button>
          <Button
            size="small"
            icon={<PlayCircleOutlined />}
            onClick={() => captureScreenshot(record.device_id)}
            disabled={record.status !== 'device'}
          >
            截图
          </Button>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    scanDevices();

    // 定时刷新设备列表
    const interval = setInterval(scanDevices, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div>
      <Card
        title="Android设备管理"
        extra={
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            loading={loading}
            onClick={scanDevices}
          >
            扫描设备
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={devices}
          rowKey="device_id"
          loading={loading}
          pagination={false}
        />
      </Card>

      {/* 设备详情弹窗 */}
      <Modal
        title={`设备详情 - ${selectedDevice?.device_id}`}
        open={deviceDetailVisible}
        onCancel={() => setDeviceDetailVisible(false)}
        footer={null}
        width={800}
      >
        {deviceInfo && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="设备ID">
              {deviceInfo.device_id}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              {getStatusTag(deviceInfo.status)}
            </Descriptions.Item>
            <Descriptions.Item label="制造商">
              {deviceInfo.properties?.manufacturer || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="型号">
              {deviceInfo.properties?.model || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="Android版本">
              {deviceInfo.properties?.version || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="API级别">
              {deviceInfo.properties?.api_level || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="已安装应用" span={2}>
              {deviceInfo.installed_apps?.length || 0} 个应用
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default AndroidDeviceManagement;
```

#### Android测试创建页面
```typescript
/**
 * Android测试创建页面
 * 提供界面分析、脚本生成功能
 */
import React, { useState } from 'react';
import {
  Card,
  Steps,
  Button,
  Upload,
  Input,
  Select,
  message,
  Spin,
  Row,
  Col,
  Image,
  Typography
} from 'antd';
import {
  UploadOutlined,
  AndroidOutlined,
  CodeOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { androidApi } from '@/services/androidApi';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Paragraph } = Typography;

interface AnalysisResult {
  ui_elements: any[];
  test_scenarios: string[];
  test_steps: any[];
  analysis_summary: string;
}

const AndroidTestCreation: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<string>('');
  const [screenshot, setScreenshot] = useState<string>('');
  const [testDescription, setTestDescription] = useState<string>('');
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [generatedScript, setGeneratedScript] = useState<string>('');
  const [devices, setDevices] = useState<any[]>([]);

  // 步骤配置
  const steps = [
    {
      title: '选择设备',
      description: '选择要测试的Android设备',
    },
    {
      title: '界面分析',
      description: '分析应用界面和UI元素',
    },
    {
      title: '生成脚本',
      description: '生成自动化测试脚本',
    },
    {
      title: '执行测试',
      description: '运行生成的测试脚本',
    },
  ];

  // 扫描设备
  const scanDevices = async () => {
    try {
      const response = await androidApi.scanDevices();
      if (response.success) {
        setDevices(response.devices.filter(d => d.status === 'device'));
      }
    } catch (error) {
      message.error('设备扫描失败');
    }
  };

  // 捕获截图并分析
  const captureAndAnalyze = async () => {
    if (!selectedDevice) {
      message.error('请先选择设备');
      return;
    }

    setLoading(true);
    try {
      // 捕获截图
      const screenshotResponse = await androidApi.captureScreenshot(selectedDevice);
      if (screenshotResponse.success) {
        setScreenshot(screenshotResponse.screenshot);

        // 进行界面分析
        const analysisResponse = await androidApi.analyzeScreenshot({
          session_id: `session_${Date.now()}`,
          screenshot_data: screenshotResponse.screenshot,
          ui_hierarchy: screenshotResponse.ui_hierarchy,
          test_description: testDescription,
          device_info: { device_id: selectedDevice }
        });

        if (analysisResponse.success) {
          setAnalysisResult(analysisResponse.analysis_result);
          setCurrentStep(2);
          message.success('界面分析完成');
        }
      }
    } catch (error) {
      message.error('界面分析失败');
    } finally {
      setLoading(false);
    }
  };

  // 生成测试脚本
  const generateScript = async () => {
    if (!analysisResult) {
      message.error('请先完成界面分析');
      return;
    }

    setLoading(true);
    try {
      const response = await androidApi.generateScript({
        session_id: `session_${Date.now()}`,
        analysis_result: analysisResult,
        test_description: testDescription,
        script_format: 'midscene-android'
      });

      if (response.success) {
        setGeneratedScript(response.generated_script.content);
        setCurrentStep(3);
        message.success('脚本生成完成');
      }
    } catch (error) {
      message.error('脚本生成失败');
    } finally {
      setLoading(false);
    }
  };

  // 执行测试脚本
  const executeScript = async () => {
    if (!generatedScript) {
      message.error('请先生成测试脚本');
      return;
    }

    setLoading(true);
    try {
      const response = await androidApi.executeScript({
        execution_id: `exec_${Date.now()}`,
        device_id: selectedDevice,
        script_content: generatedScript,
        script_type: 'midscene-android'
      });

      if (response.success) {
        message.success('测试脚本开始执行');
        // 这里可以跳转到执行监控页面
      }
    } catch (error) {
      message.error('脚本执行失败');
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    scanDevices();
  }, []);

  return (
    <div>
      <Card title="Android测试创建">
        <Steps current={currentStep} items={steps} style={{ marginBottom: 24 }} />

        <Spin spinning={loading}>
          {/* 步骤1: 选择设备 */}
          {currentStep === 0 && (
            <Card title="选择测试设备">
              <Row gutter={16}>
                <Col span={12}>
                  <Select
                    style={{ width: '100%' }}
                    placeholder="选择Android设备"
                    value={selectedDevice}
                    onChange={setSelectedDevice}
                  >
                    {devices.map(device => (
                      <Option key={device.device_id} value={device.device_id}>
                        {device.device_id} ({device.properties?.model || '未知型号'})
                      </Option>
                    ))}
                  </Select>
                </Col>
                <Col span={12}>
                  <Button onClick={scanDevices}>
                    刷新设备列表
                  </Button>
                </Col>
              </Row>

              <div style={{ marginTop: 16 }}>
                <TextArea
                  placeholder="请描述要测试的功能或场景..."
                  value={testDescription}
                  onChange={(e) => setTestDescription(e.target.value)}
                  rows={4}
                />
              </div>

              <div style={{ marginTop: 16 }}>
                <Button
                  type="primary"
                  onClick={() => setCurrentStep(1)}
                  disabled={!selectedDevice || !testDescription}
                >
                  下一步
                </Button>
              </div>
            </Card>
          )}

          {/* 步骤2: 界面分析 */}
          {currentStep === 1 && (
            <Card title="界面分析">
              <Row gutter={16}>
                <Col span={12}>
                  <Button
                    type="primary"
                    icon={<AndroidOutlined />}
                    onClick={captureAndAnalyze}
                    loading={loading}
                  >
                    捕获截图并分析
                  </Button>
                </Col>
              </Row>

              {screenshot && (
                <div style={{ marginTop: 16 }}>
                  <Title level={5}>设备截图</Title>
                  <Image src={screenshot} width={300} />
                </div>
              )}
            </Card>
          )}

          {/* 步骤3: 生成脚本 */}
          {currentStep === 2 && analysisResult && (
            <Card title="脚本生成">
              <Row gutter={16}>
                <Col span={12}>
                  <Title level={5}>分析结果</Title>
                  <Paragraph>
                    识别到 {analysisResult.ui_elements.length} 个UI元素，
                    生成了 {analysisResult.test_scenarios.length} 个测试场景。
                  </Paragraph>
                  <Paragraph>{analysisResult.analysis_summary}</Paragraph>
                </Col>
                <Col span={12}>
                  <Button
                    type="primary"
                    icon={<CodeOutlined />}
                    onClick={generateScript}
                    loading={loading}
                  >
                    生成测试脚本
                  </Button>
                </Col>
              </Row>
            </Card>
          )}

          {/* 步骤4: 执行测试 */}
          {currentStep === 3 && generatedScript && (
            <Card title="执行测试">
              <div style={{ marginBottom: 16 }}>
                <Title level={5}>生成的测试脚本</Title>
                <TextArea
                  value={generatedScript}
                  rows={10}
                  readOnly
                  style={{ fontFamily: 'monospace' }}
                />
              </div>

              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={executeScript}
                loading={loading}
              >
                执行测试脚本
              </Button>
            </Card>
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default AndroidTestCreation;
```

### 5. 配置文件和环境设置

#### 环境变量配置 (.env)
```bash
# Android自动化配置
ANDROID_HOME=/Users/<USER>/Library/Android/sdk
ADB_PATH=adb
ADB_TIMEOUT=30
MIDSCENE_ANDROID_TIMEOUT=300
AUTO_DISMISS_KEYBOARD=true
IME_STRATEGY=always-yadb

# 设备管理配置
DEVICE_SCAN_INTERVAL=30
MAX_CONCURRENT_DEVICES=5
DEVICE_HEALTH_CHECK_INTERVAL=60

# Appium配置（备用）
APPIUM_SERVER_URL=http://localhost:4723
APPIUM_TIMEOUT=60

# AI模型配置（复用现有配置）
DEEPSEEK_API_KEY=your-deepseek-api-key
QWEN_VL_API_KEY=your-qwen-vl-api-key
UI_TARS_API_KEY=your-ui-tars-api-key
```

#### Docker配置文件
```dockerfile
# Dockerfile.android
FROM node:18-slim

# 安装Android SDK和ADB
RUN apt-get update && apt-get install -y \
    wget \
    unzip \
    openjdk-11-jdk \
    && rm -rf /var/lib/apt/lists/*

# 设置Android SDK
ENV ANDROID_HOME=/opt/android-sdk
ENV PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools

# 下载并安装Android SDK
RUN mkdir -p $ANDROID_HOME && \
    cd $ANDROID_HOME && \
    wget https://dl.google.com/android/repository/commandlinetools-linux-8512546_latest.zip && \
    unzip commandlinetools-linux-8512546_latest.zip && \
    rm commandlinetools-linux-8512546_latest.zip

# 安装platform-tools (包含adb)
RUN cd $ANDROID_HOME && \
    yes | cmdline-tools/bin/sdkmanager --sdk_root=$ANDROID_HOME "platform-tools"

# 设置工作目录
WORKDIR /app

# 复制package.json并安装依赖
COPY package*.json ./
RUN npm install

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["npm", "start"]
```

#### Docker Compose配置
```yaml
# docker-compose.android.yml
version: '3.8'

services:
  ui-automation-android:
    build:
      context: .
      dockerfile: Dockerfile.android
    ports:
      - "8000:8000"
    environment:
      - ANDROID_HOME=/opt/android-sdk
      - ADB_PATH=/opt/android-sdk/platform-tools/adb
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - /dev/bus/usb:/dev/bus/usb  # USB设备访问
    privileged: true  # 需要访问USB设备
    networks:
      - ui-automation-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - ui-automation-network

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: ui_automation
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - ui-automation-network

volumes:
  mysql_data:

networks:
  ui-automation-network:
    driver: bridge
```

### 6. 部署和运维脚本

#### 自动化部署脚本
```bash
#!/bin/bash
# deploy-android.sh - Android模块部署脚本

set -e

echo "🚀 开始部署Android自动化模块..."

# 检查环境依赖
check_dependencies() {
    echo "📋 检查环境依赖..."

    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js未安装"
        exit 1
    fi

    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker未安装"
        exit 1
    fi

    # 检查Android SDK
    if [ -z "$ANDROID_HOME" ]; then
        echo "❌ ANDROID_HOME环境变量未设置"
        exit 1
    fi

    echo "✅ 环境依赖检查通过"
}

# 构建前端
build_frontend() {
    echo "🔨 构建前端Android模块..."
    cd frontend
    npm install
    npm run build:android
    cd ..
}

# 构建后端
build_backend() {
    echo "🔨 构建后端Android模块..."
    cd backend
    pip install -r requirements.txt
    python -m pytest tests/android/ -v
    cd ..
}

# 数据库迁移
migrate_database() {
    echo "📊 执行数据库迁移..."
    cd backend
    python -m alembic upgrade head
    cd ..
}

# 启动服务
start_services() {
    echo "🚀 启动Android自动化服务..."
    docker-compose -f docker-compose.android.yml up -d

    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 30

    # 健康检查
    if curl -f http://localhost:8000/health; then
        echo "✅ 服务启动成功"
    else
        echo "❌ 服务启动失败"
        exit 1
    fi
}

# 执行部署
main() {
    check_dependencies
    build_frontend
    build_backend
    migrate_database
    start_services

    echo "🎉 Android自动化模块部署完成！"
    echo "📱 访问地址: http://localhost:3000/android"
    echo "📚 API文档: http://localhost:8000/docs"
}

main "$@"
```

## 🧪 测试策略和质量保证

### 1. 单元测试策略

#### Android智能体单元测试
```python
"""
Android智能体单元测试
确保各个智能体功能正确性
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch

from app.agents.android.analyzer_agent import AndroidAnalyzerAgent
from app.core.messages.android import AndroidAnalysisRequest

class TestAndroidAnalyzerAgent:
    """Android分析智能体测试"""

    @pytest.fixture
    def mock_model_client(self):
        """模拟AI模型客户端"""
        client = Mock()
        client.create_completion = AsyncMock(return_value=Mock(
            choices=[Mock(message=Mock(content='{"ui_elements": [], "test_scenarios": []}'))]
        ))
        return client

    @pytest.fixture
    def analyzer_agent(self, mock_model_client):
        """创建分析智能体实例"""
        return AndroidAnalyzerAgent(model_client=mock_model_client)

    @pytest.mark.asyncio
    async def test_handle_analysis_request(self, analyzer_agent):
        """测试分析请求处理"""
        request = AndroidAnalysisRequest(
            session_id="test_session",
            screenshot_data="data:image/png;base64,test_data",
            test_description="测试登录功能",
            device_info={"device_id": "test_device"}
        )

        result = await analyzer_agent.handle_analysis_request(request, Mock())

        assert result is not None
        assert hasattr(result, 'ui_elements')
        assert hasattr(result, 'test_scenarios')

    @pytest.mark.asyncio
    async def test_build_analysis_prompt(self, analyzer_agent):
        """测试分析提示词构建"""
        request = AndroidAnalysisRequest(
            session_id="test_session",
            screenshot_data="test_data",
            test_description="测试功能",
            ui_hierarchy="<hierarchy>test</hierarchy>",
            device_info={"platform": "Android"}
        )

        prompt = analyzer_agent._build_analysis_prompt(request)

        assert "测试功能" in prompt
        assert "hierarchy" in prompt
        assert "Android" in prompt
```

#### 设备管理服务测试
```python
"""
设备管理服务测试
验证设备连接、监控、操作功能
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch

from app.services.android.device_service import DeviceManagementService

class TestDeviceManagementService:
    """设备管理服务测试"""

    @pytest.fixture
    def device_service(self):
        """创建设备管理服务实例"""
        return DeviceManagementService()

    @pytest.mark.asyncio
    async def test_scan_connected_devices(self, device_service):
        """测试设备扫描"""
        with patch.object(device_service, '_run_adb_command') as mock_adb:
            mock_adb.return_value = "List of devices attached\ntest_device\tdevice\n"

            devices = await device_service.scan_connected_devices()

            assert len(devices) == 1
            assert devices[0]['device_id'] == 'test_device'
            assert devices[0]['status'] == 'device'

    @pytest.mark.asyncio
    async def test_capture_screenshot(self, device_service):
        """测试截图捕获"""
        with patch.object(device_service, '_run_adb_command') as mock_adb:
            mock_adb.return_value = b'fake_screenshot_data'

            screenshot = await device_service.capture_screenshot('test_device')

            assert screenshot.startswith('data:image/png;base64,')
            mock_adb.assert_called_with([
                '-s', 'test_device', 'exec-out', 'screencap', '-p'
            ], return_bytes=True)
```

### 2. 集成测试策略

#### 端到端测试流程
```python
"""
Android模块端到端测试
验证完整的测试创建和执行流程
"""
import pytest
import asyncio
from fastapi.testclient import TestClient

from app.main import app
from app.core.config import get_settings

class TestAndroidE2E:
    """Android端到端测试"""

    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)

    @pytest.fixture
    def mock_device_id(self):
        """模拟设备ID"""
        return "emulator-5554"

    def test_complete_android_workflow(self, client, mock_device_id):
        """测试完整的Android工作流程"""

        # 1. 扫描设备
        response = client.get("/api/v1/android/devices/scan")
        assert response.status_code == 200
        assert response.json()["success"] is True

        # 2. 捕获截图
        response = client.post(f"/api/v1/android/devices/{mock_device_id}/screenshot")
        assert response.status_code == 200
        screenshot_data = response.json()["screenshot"]

        # 3. 分析界面
        analysis_request = {
            "session_id": "test_session",
            "screenshot_data": screenshot_data,
            "test_description": "测试Android应用登录功能",
            "device_info": {"device_id": mock_device_id}
        }
        response = client.post("/api/v1/android/analyze/screenshot", json=analysis_request)
        assert response.status_code == 200
        analysis_result = response.json()["analysis_result"]

        # 4. 生成脚本
        script_request = {
            "session_id": "test_session",
            "analysis_result": analysis_result,
            "test_description": "测试Android应用登录功能",
            "script_format": "midscene-android"
        }
        response = client.post("/api/v1/android/scripts/generate", json=script_request)
        assert response.status_code == 200
        generated_script = response.json()["generated_script"]

        # 5. 执行脚本
        execution_request = {
            "execution_id": "test_execution",
            "device_id": mock_device_id,
            "script_content": generated_script["content"],
            "script_type": "midscene-android"
        }
        response = client.post("/api/v1/android/execution/execute", json=execution_request)
        assert response.status_code == 200
        assert response.json()["status"] == "started"
```

### 3. 性能测试策略

#### 并发设备管理测试
```python
"""
Android模块性能测试
验证多设备并发处理能力
"""
import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor

class TestAndroidPerformance:
    """Android性能测试"""

    @pytest.mark.asyncio
    async def test_concurrent_device_operations(self):
        """测试并发设备操作"""
        device_ids = [f"device_{i}" for i in range(5)]

        async def capture_screenshot(device_id):
            """模拟截图捕获"""
            start_time = time.time()
            # 模拟截图操作
            await asyncio.sleep(0.1)
            return time.time() - start_time

        # 并发执行截图操作
        tasks = [capture_screenshot(device_id) for device_id in device_ids]
        results = await asyncio.gather(*tasks)

        # 验证所有操作都在合理时间内完成
        assert all(duration < 1.0 for duration in results)
        assert max(results) < 0.5  # 最长不超过500ms

    @pytest.mark.asyncio
    async def test_script_generation_performance(self):
        """测试脚本生成性能"""
        large_analysis_result = {
            "ui_elements": [{"type": "button", "text": f"Button {i}"} for i in range(100)],
            "test_scenarios": [f"Scenario {i}" for i in range(20)],
            "analysis_summary": "Large UI with many elements"
        }

        start_time = time.time()

        # 模拟脚本生成
        await asyncio.sleep(0.5)  # 模拟AI处理时间

        generation_time = time.time() - start_time

        # 验证生成时间在可接受范围内
        assert generation_time < 2.0  # 不超过2秒
```

## ⚠️ 风险评估和缓解策略

### 1. 技术风险

#### 设备连接稳定性风险
**风险描述**: Android设备连接不稳定，可能导致测试中断
**影响程度**: 高
**缓解策略**:
- 实现设备连接健康检查机制
- 自动重连和故障转移
- 支持多设备备份方案
- 网络ADB连接作为USB连接的备选

#### AI模型识别准确性风险
**风险描述**: AI模型对Android界面识别不准确，影响脚本质量
**影响程度**: 中
**缓解策略**:
- 使用多个AI模型进行交叉验证
- 建立Android界面识别训练数据集
- 提供人工校正和反馈机制
- 逐步优化提示词和模型参数

#### 脚本执行兼容性风险
**风险描述**: 生成的脚本在不同Android版本或设备上兼容性问题
**影响程度**: 中
**缓解策略**:
- 建立多版本Android测试环境
- 实现脚本兼容性检查
- 提供设备特定的脚本优化
- 建立脚本模板库

### 2. 运维风险

#### 资源消耗风险
**风险描述**: Android自动化测试消耗大量系统资源
**影响程度**: 中
**缓解策略**:
- 实现资源使用监控和限制
- 优化脚本执行效率
- 实现任务队列和负载均衡
- 提供资源清理机制

#### 数据安全风险
**风险描述**: 测试过程中可能涉及敏感数据泄露
**影响程度**: 高
**缓解策略**:
- 实现数据加密存储和传输
- 建立访问权限控制
- 定期清理临时文件和日志
- 遵循数据保护法规

### 3. 业务风险

#### 学习成本风险
**风险描述**: 团队需要时间学习Android自动化新功能
**影响程度**: 低
**缓解策略**:
- 提供详细的使用文档和教程
- 组织培训和技术分享
- 建立最佳实践指南
- 提供技术支持和答疑

#### 维护成本风险
**风险描述**: Android生态变化可能导致维护成本增加
**影响程度**: 中
**缓解策略**:
- 建立版本兼容性测试流程
- 关注Android生态发展趋势
- 建立社区合作和技术交流
- 制定长期技术演进规划

## 📊 成功指标和监控

### 1. 技术指标

#### 功能性指标
- **设备连接成功率**: ≥ 95%
- **界面分析准确率**: ≥ 85%
- **脚本生成成功率**: ≥ 90%
- **脚本执行成功率**: ≥ 80%

#### 性能指标
- **设备扫描响应时间**: ≤ 5秒
- **界面分析处理时间**: ≤ 30秒
- **脚本生成时间**: ≤ 60秒
- **并发设备支持数**: ≥ 5个

#### 稳定性指标
- **系统可用性**: ≥ 99%
- **错误恢复时间**: ≤ 30秒
- **内存使用率**: ≤ 80%
- **CPU使用率**: ≤ 70%

### 2. 业务指标

#### 用户体验指标
- **用户满意度**: ≥ 4.0/5.0
- **功能使用率**: ≥ 60%
- **用户反馈响应时间**: ≤ 24小时
- **培训完成率**: ≥ 90%

#### 效率指标
- **测试创建时间减少**: ≥ 50%
- **测试执行效率提升**: ≥ 30%
- **缺陷发现率提升**: ≥ 25%
- **回归测试时间减少**: ≥ 40%

### 3. 监控和告警

#### 实时监控指标
```python
# 监控指标定义
MONITORING_METRICS = {
    "device_connection_rate": {
        "threshold": 0.95,
        "alert_level": "warning"
    },
    "analysis_success_rate": {
        "threshold": 0.85,
        "alert_level": "error"
    },
    "script_execution_time": {
        "threshold": 300,  # 5分钟
        "alert_level": "warning"
    },
    "system_memory_usage": {
        "threshold": 0.8,
        "alert_level": "critical"
    }
}
```

#### 告警策略
- **即时告警**: 系统错误、设备离线、执行失败
- **定期报告**: 每日性能报告、每周使用统计
- **趋势分析**: 月度性能趋势、季度业务指标分析

---

*本Android自动化测试技术落地方案提供了完整的技术架构、实现方案、测试策略和风险管控措施。通过系统性的规划和详细的实施指导，确保Android自动化能力能够稳定、高效地集成到现有UI自动化测试系统中，为团队提供强大的移动端测试能力。*
