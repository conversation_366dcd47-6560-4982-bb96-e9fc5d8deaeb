/* YAML查看器样式 */
.yaml-viewer {
  margin-top: 24px;
}

.viewer-card {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.viewer-card .ant-card-head {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #fafafa, #f0f0f0);
}

.viewer-card .ant-card-body {
  padding: 0;
}

/* 标签页样式 */
.viewer-card .ant-tabs {
  margin: 0;
}

.viewer-card .ant-tabs-content-holder {
  padding: 24px;
}

.viewer-card .ant-tabs-tab {
  font-weight: 500;
  margin: 0 8px;
}

/* YAML标签页 */
.yaml-tab {
  max-height: 600px;
  overflow: auto;
}

.yaml-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.yaml-content {
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
}

.yaml-content pre {
  margin: 0 !important;
  max-height: 500px;
  overflow: auto;
}

/* 动作序列标签页 */
.actions-tab {
  max-height: 600px;
  overflow: auto;
}

.actions-summary {
  margin-bottom: 24px;
}

.actions-summary .stat-item {
  text-align: center;
  padding: 12px;
}

.actions-summary .stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
  margin-top: 4px;
}

.action-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 0;
}

.action-item:last-child {
  border-bottom: none;
}

.action-content {
  width: 100%;
}

.action-header {
  margin-bottom: 8px;
}

.action-description {
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 1.5;
}

.action-task {
  font-size: 12px;
  opacity: 0.7;
}

/* 分析结果标签页 */
.analysis-tab {
  max-height: 600px;
  overflow: auto;
}

.info-card {
  margin-bottom: 16px;
}

.info-card .ant-card-head {
  padding: 12px 16px;
  min-height: auto;
}

.info-card .ant-card-body {
  padding: 16px;
}

.page-description {
  margin-top: 12px;
  margin-bottom: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

.elements-card {
  margin-bottom: 16px;
}

.element-item {
  width: 100%;
}

.element-header {
  margin-bottom: 8px;
}

.interaction-hint {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  font-size: 12px;
  color: #52c41a;
}

.interaction-hint .anticon {
  margin-right: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .viewer-card .ant-tabs-content-holder {
    padding: 16px;
  }
  
  .yaml-tab,
  .actions-tab,
  .analysis-tab {
    max-height: 400px;
  }
  
  .yaml-content pre {
    max-height: 300px;
    font-size: 12px;
  }
  
  .actions-summary .ant-col {
    margin-bottom: 8px;
  }
}

/* 代码高亮样式调整 */
.yaml-content .token.key {
  color: #1890ff;
}

.yaml-content .token.string {
  color: #52c41a;
}

.yaml-content .token.number {
  color: #fa8c16;
}

.yaml-content .token.boolean {
  color: #722ed1;
}

/* 滚动条样式 */
.yaml-tab::-webkit-scrollbar,
.actions-tab::-webkit-scrollbar,
.analysis-tab::-webkit-scrollbar {
  width: 6px;
}

.yaml-tab::-webkit-scrollbar-track,
.actions-tab::-webkit-scrollbar-track,
.analysis-tab::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.yaml-tab::-webkit-scrollbar-thumb,
.actions-tab::-webkit-scrollbar-thumb,
.analysis-tab::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.yaml-tab::-webkit-scrollbar-thumb:hover,
.actions-tab::-webkit-scrollbar-thumb:hover,
.analysis-tab::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.viewer-card {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-item {
  transition: all 0.3s ease;
}

.action-item:hover {
  background: rgba(24, 144, 255, 0.02);
  border-radius: 4px;
  margin: 0 -8px;
  padding: 16px 8px;
}

/* 标签颜色 */
.ant-tag-blue {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.ant-tag-green {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.ant-tag-orange {
  background: #fff7e6;
  border-color: #ffd591;
  color: #fa8c16;
}

.ant-tag-purple {
  background: #f9f0ff;
  border-color: #d3adf7;
  color: #722ed1;
}

.ant-tag-cyan {
  background: #e6fffb;
  border-color: #87e8de;
  color: #13c2c2;
}

.ant-tag-red {
  background: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

.ant-tag-gold {
  background: #fffbe6;
  border-color: #ffe58f;
  color: #faad14;
}

.ant-tag-gray {
  background: #fafafa;
  border-color: #d9d9d9;
  color: #666;
}

/* 徽章样式 */
.ant-badge-count {
  font-size: 12px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  padding: 0 4px;
}

/* 描述列表样式 */
.ant-descriptions-item-label {
  font-weight: 500;
  color: #666;
}

.ant-descriptions-item-content {
  color: #262626;
}

/* 列表样式 */
.ant-list-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 0;
}

.ant-list-item:last-child {
  border-bottom: none;
}

/* 空状态样式 */
.ant-empty {
  margin: 32px 0;
}

.ant-empty-description {
  color: #999;
}
