# MidScene.js Android SDK 集成示例

基于MidScene.js Android SDK优化的Android脚本生成智能体使用示例。

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装Node.js依赖
npm install @midscene/android dotenv tsx

# 设置环境变量 (.env文件)
OPENAI_API_KEY=sk-your-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4-vision-preview

# 确保ADB已安装并设备已连接
adb devices
```

### 2. 基础使用示例

```python
from app.agents.android import AndroidScriptGeneratorAgent

# 创建脚本生成智能体
generator = AndroidScriptGeneratorAgent()

# 模拟界面分析结果
analysis_result = {
    "ui_elements": [
        {
            "description": "登录按钮",
            "text": "登录",
            "resource_id": "com.example.app:id/btn_login",
            "clickable": True
        },
        {
            "description": "用户名输入框",
            "resource_id": "com.example.app:id/et_username",
            "element_type": "EditText"
        }
    ],
    "test_scenarios": [
        "用户登录流程测试",
        "输入验证测试"
    ],
    "analysis_summary": "这是一个登录页面，包含用户名输入框、密码输入框和登录按钮"
}

# 生成MidScene.js脚本
scripts = await generator.generate_android_script(
    analysis_result=analysis_result,
    test_description="测试用户登录功能",
    script_formats=["midscene", "vitest"]  # 优先生成MidScene.js格式
)

print("生成的脚本:")
for script in scripts:
    print(f"格式: {script['format']}")
    print(f"框架: {script['framework']}")
    print(f"内容:\n{script['content']}")
```

## 📝 生成的脚本示例

### MidScene.js TypeScript 脚本

```typescript
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config";

const sleep = (ms: number) => new Promise(r => setTimeout(r, ms));

async function testUserLogin() {
    // 获取连接的设备
    const devices = await getConnectedDevices();
    if (devices.length === 0) {
        throw new Error('没有找到连接的Android设备');
    }
    
    const page = new AndroidDevice(devices[0].udid, {
        autoDismissKeyboard: true,
        keyboardDismissStrategy: 'esc-first',
    });

    // 初始化MidScene智能体
    const agent = new AndroidAgent(page, {
        aiActionContext: '自动处理权限弹窗、位置请求、用户协议等系统弹窗',
    });

    try {
        await page.connect();
        console.log('设备连接成功');
        
        // 启动应用
        await page.launch('com.example.app');
        await sleep(3000);
        
        // 🎯 AI驱动的登录操作
        await agent.aiAction('点击登录按钮');
        await agent.aiWaitFor('登录页面加载完成，显示用户名和密码输入框');
        
        await agent.aiAction('在用户名输入框输入 "testuser"');
        await agent.aiAction('在密码输入框输入 "password123"');
        await agent.aiAction('点击登录按钮');
        
        // 🔍 智能等待登录完成
        await agent.aiWaitFor('登录成功，显示用户主页面');
        
        // 📊 验证登录结果
        const loginStatus = await agent.aiBoolean('用户是否已经成功登录？');
        console.log('登录状态:', loginStatus);
        
        if (loginStatus) {
            const userInfo = await agent.aiQuery(
                '{username: string, email?: string}, 获取当前登录用户的信息'
            );
            console.log('用户信息:', userInfo);
            
            // ✅ AI断言验证
            await agent.aiAssert('页面显示了用户的个人信息');
            await agent.aiAssert('用户已成功登录到系统');
        }
        
        console.log('✅ 登录测试完成');
        
    } catch (error) {
        console.error('❌ 测试执行失败:', error);
        throw error;
    } finally {
        await page.disconnect();
        console.log('设备连接已断开');
    }
}

// 执行测试
testUserLogin().catch(console.error);
```

### Vitest + MidScene.js 测试脚本

```typescript
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';

describe('Android应用登录测试', () => {
    let agent: AndroidAgent;
    let page: AndroidDevice;

    beforeAll(async () => {
        const devices = await getConnectedDevices();
        expect(devices.length).toBeGreaterThan(0);
        
        page = new AndroidDevice(devices[0].udid, {
            autoDismissKeyboard: true,
        });
        
        agent = new AndroidAgent(page, {
            aiActionContext: '自动处理系统弹窗和权限请求',
        });
        
        await page.connect();
        await page.launch('com.example.app');
    }, 30000);

    afterAll(async () => {
        if (page) {
            await page.disconnect();
        }
    });

    it('应用启动测试', async () => {
        await agent.aiWaitFor('应用启动完成，显示登录界面');
        
        const hasLoginButton = await agent.aiBoolean('页面是否显示登录按钮？');
        expect(hasLoginButton).toBe(true);
    });

    it('用户登录流程测试', async () => {
        // 执行登录操作
        await agent.aiAction('点击登录按钮');
        await agent.aiAction('在用户名输入框输入 "testuser"，在密码输入框输入 "password123"');
        await agent.aiAction('点击登录按钮');
        
        // 等待登录完成
        await agent.aiWaitFor('登录成功，显示用户主页');
        
        // 验证登录结果
        const isLoggedIn = await agent.aiBoolean('用户是否已成功登录？');
        expect(isLoggedIn).toBe(true);
        
        // 验证用户信息
        const welcomeMessage = await agent.aiString('欢迎消息的内容是什么？');
        expect(welcomeMessage).toContain('欢迎');
        
        // AI断言
        await agent.aiAssert('页面显示了用户的个人信息');
    }, 60000);

    it('数据提取测试', async () => {
        const userProfile = await agent.aiQuery(
            '{username: string, email: string, avatar?: string}, 获取用户资料信息'
        );
        
        expect(userProfile).toHaveProperty('username');
        expect(userProfile).toHaveProperty('email');
        expect(userProfile.username).toBe('testuser');
    });
});
```

### YAML格式脚本

```typescript
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config";

async function runYamlTest() {
    const devices = await getConnectedDevices();
    const page = new AndroidDevice(devices[0].udid);
    const agent = new AndroidAgent(page, {
        aiActionContext: '自动处理权限弹窗和系统对话框',
    });

    await page.connect();
    await page.launch('com.example.app');
    
    // 🎯 运行YAML格式的测试脚本
    const { result } = await agent.runYaml(`
tasks:
  - name: 用户登录流程
    flow:
      - ai: 点击登录按钮
      - aiWaitFor: 登录页面加载完成
      - ai: 在用户名输入框输入 "testuser"，在密码输入框输入 "password123"
      - ai: 点击登录按钮
      - aiWaitFor: 登录成功，显示主页面
      
  - name: 用户信息验证
    flow:
      - aiQuery: "{username: string, email: string}, 获取用户信息"
        name: userInfo
      - aiBoolean: "用户是否已成功登录？"
        name: loginStatus
      - aiString: "欢迎消息的内容"
        name: welcomeMessage
      - aiNumber: "页面上显示了多少个菜单项？"
        name: menuCount
        
  - name: 功能验证
    flow:
      - aiAssert: "用户已成功登录"
      - aiAssert: "页面显示了用户个人信息"
      - aiAssert: "导航菜单正常显示"
`);
    
    console.log('YAML测试结果:', result);
    await page.disconnect();
}

runYamlTest().catch(console.error);
```

## 🎯 高级功能示例

### 1. 电商应用测试

```typescript
async function ecommerceTest() {
    const devices = await getConnectedDevices();
    const page = new AndroidDevice(devices[0].udid);
    const agent = new AndroidAgent(page);

    await page.connect();
    
    // 打开电商网站
    await agent.launch('https://www.ebay.com');
    await agent.aiWaitFor('eBay首页加载完成');
    
    // 搜索商品
    await agent.aiAction('在搜索框输入 "Headphones"，点击搜索按钮');
    await agent.aiWaitFor('搜索结果页面加载完成，显示耳机商品列表');
    
    // 提取商品信息
    const products = await agent.aiQuery(
        '{itemTitle: string, price: number, rating?: number}[], 获取搜索结果中的商品信息'
    );
    
    console.log('搜索到的商品:', products);
    
    // 验证搜索结果
    await agent.aiAssert('页面显示了耳机搜索结果');
    await agent.aiAssert('左侧有分类筛选器');
    
    const productCount = await agent.aiNumber('搜索结果显示了多少个商品？');
    console.log('商品数量:', productCount);
    
    await page.disconnect();
}
```

### 2. 原生应用设置测试

```typescript
async function settingsTest() {
    const devices = await getConnectedDevices();
    const page = new AndroidDevice(devices[0].udid);
    const agent = new AndroidAgent(page);

    await page.connect();
    
    // 打开系统设置
    await agent.launch('com.android.settings');
    await agent.aiWaitFor('设置页面加载完成');
    
    // 查找并点击应用管理
    await agent.aiAction('查找并点击"应用管理"或"应用"选项');
    await agent.aiWaitFor('应用管理页面加载完成');
    
    // 获取已安装应用数量
    const appCount = await agent.aiNumber('页面显示了多少个已安装的应用？');
    console.log('已安装应用数量:', appCount);
    
    // 验证页面内容
    await agent.aiAssert('页面显示了已安装应用列表');
    
    await page.disconnect();
}
```

## 🔧 配置和最佳实践

### 1. 环境配置

```typescript
// 自定义设备配置
const page = new AndroidDevice('device_id', {
    autoDismissKeyboard: true,
    keyboardDismissStrategy: 'esc-first',
    imeStrategy: 'always-yadb',
    androidAdbPath: '/custom/path/to/adb',
    remoteAdbHost: '*************',
    remoteAdbPort: 5037,
});

// AI智能体配置
const agent = new AndroidAgent(page, {
    aiActionContext: '自动处理权限弹窗、位置请求、用户协议等',
    // 其他配置选项
});
```

### 2. 错误处理

```typescript
async function robustTest() {
    let page: AndroidDevice | null = null;
    
    try {
        const devices = await getConnectedDevices();
        if (devices.length === 0) {
            throw new Error('没有找到连接的设备');
        }
        
        page = new AndroidDevice(devices[0].udid);
        const agent = new AndroidAgent(page);
        
        await page.connect();
        
        // 重试机制
        const maxRetries = 3;
        for (let i = 0; i < maxRetries; i++) {
            try {
                await agent.aiAction('执行关键操作');
                break;
            } catch (error) {
                if (i === maxRetries - 1) throw error;
                console.log(`重试第 ${i + 1} 次...`);
                await new Promise(r => setTimeout(r, 2000));
            }
        }
        
    } catch (error) {
        console.error('测试失败:', error);
        throw error;
    } finally {
        if (page) {
            await page.disconnect();
        }
    }
}
```

## 📊 与传统框架对比

| 特性 | MidScene.js | Appium | uiautomator2 |
|------|-------------|---------|--------------|
| 学习成本 | 低 | 高 | 中 |
| 元素定位 | AI自然语言 | 精确选择器 | 精确选择器 |
| 维护成本 | 低 | 高 | 中 |
| 执行速度 | 快 | 中 | 快 |
| 稳定性 | 高 | 中 | 高 |
| 跨平台 | 支持 | 支持 | Android专用 |

MidScene.js Android SDK通过AI驱动的自然语言操作，大大简化了Android自动化测试的编写和维护工作，是现代Android自动化测试的首选方案。
