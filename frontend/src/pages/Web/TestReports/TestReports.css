.test-reports-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-reports-container .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-reports-container .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.test-reports-container .ant-table-tbody > tr:hover > td {
  background: #f0f9ff;
}

.test-reports-container .ant-btn {
  border-radius: 6px;
}

.test-reports-container .ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.test-reports-container .ant-btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.test-reports-container .ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

.test-reports-container .ant-statistic {
  text-align: center;
}

.test-reports-container .ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.test-reports-container .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

.test-reports-container .ant-modal-content {
  border-radius: 8px;
}

.test-reports-container .ant-modal-body {
  padding: 16px;
}

.test-reports-container iframe {
  background: #fff;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.1);
}

/* 紧凑搜索栏样式 */
.test-reports-container .compact-search-bar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  padding: 12px 16px;
}

.test-reports-container .compact-search-bar .ant-input,
.test-reports-container .compact-search-bar .ant-select,
.test-reports-container .compact-search-bar .ant-picker {
  border-radius: 4px;
  font-size: 12px;
}

.test-reports-container .compact-search-bar .ant-btn-small {
  height: 28px;
  padding: 4px 8px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-reports-container {
    padding: 16px;
  }

  .test-reports-container .compact-search-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .test-reports-container .compact-search-bar .ant-input,
  .test-reports-container .compact-search-bar .ant-select,
  .test-reports-container .compact-search-bar .ant-picker {
    width: 100%;
    margin-bottom: 4px;
  }

  .test-reports-container .compact-search-bar .ant-btn {
    width: 100%;
    margin-bottom: 4px;
  }

  .test-reports-container .ant-table {
    font-size: 12px;
  }

  .test-reports-container .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
  }

  .test-reports-container .ant-modal {
    margin: 0;
    max-width: 100vw;
  }
}

/* 动画效果 */
.test-reports-container .ant-table-tbody > tr {
  transition: all 0.3s ease;
}

.test-reports-container .ant-btn {
  transition: all 0.3s ease;
}

.test-reports-container .ant-card {
  transition: all 0.3s ease;
}

.test-reports-container .ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}
