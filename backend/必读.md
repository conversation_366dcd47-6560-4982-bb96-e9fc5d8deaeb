# 一、智能体说明文档
## 快速参考文档
backend/docs/quick_reference.md

## 完整的详细文档
backend/docs/agents_and_apis_documentation.md

# 二、请修改 当前目录下的 .env 文件，如下内容都修改一下
## 数据库配置
```
DATABASE_URL=mysql+aiomysql://root:mysql@localhost:3306/ai_automation
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=mysql
MYSQL_DATABASE=ai_automation
```
## AI模型配置
```
# DeepSeek配置
DEEPSEEK_API_KEY="你申请的deepseek密钥"
DEEPSEEK_BASE_URL="https://api.deepseek.com/v1"
DEEPSEEK_MODEL="deepseek-chat"

# UI-TARS配置
UI_TARS_API_KEY="你申请的ui-tars密钥"
UI_TARS_BASE_URL="https://ark.cn-beijing.volces.com/api/v3"
UI_TARS_MODEL="doubao-1-5-ui-tars-250428"
UI_TARS_ENDPOINT_URL="your_huggingface_endpoint_url_here"

```

# 三、安装依赖
1. 后端依赖
进入backend目录，运行pip install -r requirements.txt
2. 前端依赖
进入frontend目录，运行npm install

# 四、启动服务
1、启动后端服务
进入backend/app目录，运行python main.py，或者打开 main.py，右键 点击 run 或者 debug
2、启动前端服务
进入frontend目录，运行npm run dev



