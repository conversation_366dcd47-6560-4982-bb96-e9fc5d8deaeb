{"name": "ui-automation-testing-frontend", "version": "1.0.0", "description": "但问智能自动化测试系统前端", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@react-three/drei": "^9.92.7", "@react-three/fiber": "^8.15.12", "@types/d3": "^7.4.3", "@types/lodash": "^4.14.202", "@types/react-syntax-highlighter": "^15.5.11", "@types/three": "^0.158.3", "@uiw/react-md-editor": "^4.0.7", "ace-builds": "^1.43.0", "ahooks": "^3.7.8", "antd": "^5.12.8", "axios": "^1.6.2", "classnames": "^2.3.2", "d3": "^7.8.5", "d3-force": "^3.0.0", "d3-selection": "^3.0.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "echarts-gl": "^2.0.9", "framer-motion": "^10.16.16", "gsap": "^3.12.2", "lodash": "^4.17.21", "react": "^18.2.0", "react-ace": "^14.0.1", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-resizable-panels": "^0.0.55", "react-router-dom": "^6.20.1", "react-syntax-highlighter": "^15.5.0", "react-use": "^17.4.2", "recharts": "^2.8.0", "three": "^0.158.0", "ui-automation-testing-frontend": "file:", "victory": "^36.6.11", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}