"""
Android界面分析智能体
负责分析Android应用界面，识别UI元素和交互流程
基于UI-TARS或Qwen-VL-Max模型进行多模态分析
"""
import json
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import MultiModalMessage
from autogen_core import message_handler, type_subscription, MessageContext
from loguru import logger

from app.core.messages.android import AndroidAnalysisRequest, AndroidAnalysisResponse
from app.core.agents.base import BaseAgent
from app.core.types import TopicTypes, AgentTypes, AGENT_NAMES, MessageRegion, AgentPlatform


@type_subscription(topic_type=TopicTypes.ANDROID_ANALYZER.value)
class AndroidAnalyzerAgent(BaseAgent):
    """Android界面分析智能体，基于AutoGen框架"""

    def __init__(self, model_client_instance=None, enable_user_feedback: bool = False, collector=None, **kwargs):
        """初始化Android界面分析智能体"""
        super().__init__(
            agent_id=AgentTypes.ANDROID_ANALYZER.value,
            agent_name=AGENT_NAMES[AgentTypes.ANDROID_ANALYZER.value],
            platform=AgentPlatform.ANDROID,
            model_client_instance=model_client_instance,
            **kwargs
        )

        self.metrics = None
        self.enable_user_feedback = enable_user_feedback
        self.collector = collector

        logger.info(f"Android界面分析智能体初始化完成，用户反馈: {enable_user_feedback}")

    @classmethod
    def create_assistant_agent(cls, model_client_instance=None, **kwargs) -> AssistantAgent:
        """创建用于Android分析的AssistantAgent实例"""
        from app.agents.factory import agent_factory

        return agent_factory.create_assistant_agent(
            name="android_analyzer",
            system_message=cls._build_system_message(),
            model_client_type="ui_tars",  # 推荐使用UI-TARS模型
            model_client_stream=True,
            **kwargs
        )

    @staticmethod
    def _build_system_message() -> str:
        """构建Android分析系统提示"""
        return """你是一个专业的Android界面分析专家，具备以下核心能力：

## 🎯 核心任务
分析Android应用界面截图和UI层次结构，识别UI元素并生成测试建议。

## 📱 Android UI元素识别能力

### 1. 基础控件识别
- **Button**: 各种按钮（普通按钮、图标按钮、浮动按钮等）
- **TextView**: 文本显示控件（标题、内容、标签等）
- **EditText**: 文本输入框（单行、多行、密码框等）
- **ImageView**: 图片显示控件（头像、图标、背景图等）
- **CheckBox/RadioButton**: 复选框和单选按钮
- **Switch/ToggleButton**: 开关控件
- **ProgressBar**: 进度条（水平、圆形、不定进度等）
- **SeekBar**: 滑动条控件

### 2. 容器布局识别
- **LinearLayout**: 线性布局（垂直、水平排列）
- **RelativeLayout**: 相对布局
- **ConstraintLayout**: 约束布局
- **FrameLayout**: 帧布局
- **RecyclerView/ListView**: 列表控件
- **ViewPager**: 页面滑动控件
- **TabLayout**: 标签页布局
- **DrawerLayout**: 侧滑抽屉布局

### 3. 导航组件识别
- **Toolbar/ActionBar**: 顶部工具栏
- **BottomNavigationView**: 底部导航栏
- **NavigationView**: 侧边导航菜单
- **FloatingActionButton**: 浮动操作按钮
- **Menu**: 菜单项（选项菜单、上下文菜单等）

### 4. 对话框和弹窗识别
- **AlertDialog**: 警告对话框
- **BottomSheetDialog**: 底部弹窗
- **PopupWindow**: 弹出窗口
- **Toast**: 提示消息
- **Snackbar**: 底部提示条

## 🔍 分析输出要求

### 1. UI元素信息
对每个识别的UI元素，提供以下信息：
```json
{
  "element_type": "Button",
  "description": "登录按钮",
  "resource_id": "com.example.app:id/btn_login",
  "text": "登录",
  "content_desc": "点击登录",
  "class_name": "android.widget.Button",
  "package_name": "com.example.app",
  "bounds": {"left": 100, "top": 200, "right": 300, "bottom": 250},
  "clickable": true,
  "scrollable": false,
  "enabled": true,
  "confidence_score": 0.95
}
```

### 2. 测试场景建议
基于界面分析，生成以下类型的测试场景：
- **功能测试**: 核心功能操作流程
- **UI交互测试**: 用户界面交互验证
- **边界测试**: 输入边界值测试
- **异常测试**: 错误处理和异常情况
- **兼容性测试**: 不同设备和系统版本适配

### 3. 测试步骤生成
为每个测试场景生成详细的测试步骤：
```json
{
  "step_number": 1,
  "action": "click",
  "target": "用户名输入框",
  "description": "点击用户名输入框获取焦点",
  "expected_result": "输入框获得焦点，显示光标",
  "selector": "resource-id('com.example.app:id/et_username')",
  "input_value": null,
  "coordinates": {"x": 200, "y": 150}
}
```

## 🎨 Android设计规范理解
- **Material Design**: 理解Google Material Design设计规范
- **适配性**: 考虑不同屏幕尺寸和密度的适配
- **无障碍性**: 关注无障碍访问特性
- **性能**: 考虑UI性能和用户体验

## 📋 输出格式要求
请以JSON格式输出分析结果，包含：
1. **ui_elements**: UI元素列表
2. **test_scenarios**: 测试场景列表  
3. **test_steps**: 详细测试步骤
4. **analysis_summary**: 分析总结
5. **confidence_score**: 整体置信度
6. **activity_name**: 当前Activity名称（如果可识别）
7. **package_name**: 应用包名（如果可识别）

请确保分析结果准确、详细，为后续的脚本生成提供充分的信息支持。"""

    def create_android_expert_agent(self) -> AssistantAgent:
        """创建Android专家分析智能体"""
        try:
            return self.create_assistant_agent(
                model_client_instance=self.model_client
            )
        except Exception as e:
            logger.error(f"创建Android专家智能体失败: {str(e)}")
            raise

    @message_handler
    async def handle_message(self, message: AndroidAnalysisRequest, ctx: MessageContext) -> None:
        """处理Android界面分析请求"""
        try:
            monitor_id = self.start_performance_monitoring()

            await self.send_response("🤖 启动Android界面分析...")

            # 创建分析智能体
            agent = self.create_android_expert_agent()

            # 准备多模态消息
            multimodal_message = await self._prepare_multimodal_message(message)

            # 运行智能体分析
            analysis_results = await self._run_agent_analysis(agent, multimodal_message)

            # 构建分析结果
            analysis_result = await self._build_analysis_result(analysis_results, message)

            self.metrics = self.end_performance_monitoring(monitor_id)

            await self.send_response(
                "✅ Android界面分析完成",
                is_final=True,
                result={
                    "analysis_result": analysis_result,
                    "user_feedback_enabled": self.enable_user_feedback,
                    "metrics": self.metrics
                }
            )

        except Exception as e:
            await self.handle_exception("handle_message", e)

    async def _prepare_multimodal_message(self, request: AndroidAnalysisRequest) -> MultiModalMessage:
        """准备多模态消息"""
        try:
            # 构建分析指导文本
            text_content = f"""
请对提供的Android应用界面进行深度分析，重点关注以下方面：

## 分析目标
{request.test_description}

## 分析要求
1. **UI元素识别**: 识别所有可交互的UI元素，包括按钮、输入框、列表等
2. **布局结构分析**: 分析界面的布局结构和层次关系
3. **交互流程设计**: 基于界面设计合理的用户交互流程
4. **测试场景生成**: 生成全面的测试场景，包括正常流程和异常情况

## 额外上下文
{request.additional_context or "无额外上下文"}

## 设备信息
{json.dumps(request.device_info, ensure_ascii=False, indent=2) if request.device_info else "无设备信息"}

## UI层次结构
{request.ui_hierarchy or "无UI层次结构信息"}

请提供详细的JSON格式分析结果。
"""

            # 构建多模态消息
            multimodal_message = MultiModalMessage(
                source="user",
                content=[
                    {"type": "text", "text": text_content.strip()},
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/png;base64,{request.screenshot_data}"}
                    }
                ]
            )

            return multimodal_message

        except Exception as e:
            logger.error(f"准备多模态消息失败: {str(e)}")
            raise

    async def _run_agent_analysis(self, agent: AssistantAgent, message: MultiModalMessage) -> Dict[str, Any]:
        """运行智能体分析"""
        try:
            await self.send_response("🔍 正在分析Android界面...")

            # 运行智能体
            from autogen_agentchat.teams import RoundRobinGroupChat
            from autogen_core.memory import ListMemory

            team = RoundRobinGroupChat([agent], memory=ListMemory())
            result = await team.run(task=message, termination_condition=lambda messages: len(messages) >= 2)

            # 获取最后一条消息内容
            if result.messages:
                last_message = result.messages[-1]
                full_content = last_message.content

                await self.send_response("📊 解析分析结果...")

                # 解析智能体输出的JSON结果
                analysis_result = await self._parse_agent_output(full_content)
                return analysis_result

            return {}

        except Exception as e:
            logger.error(f"智能体分析执行失败: {str(e)}")
            # 返回默认结果
            return {
                "ui_elements": [],
                "test_scenarios": [],
                "test_steps": [],
                "analysis_summary": "分析失败，请重试",
                "confidence_score": 0.0,
                "activity_name": None,
                "package_name": None
            }

    async def _parse_agent_output(self, content: str) -> Dict[str, Any]:
        """解析智能体输出内容"""
        try:
            # 尝试提取JSON内容
            import re
            
            # 查找JSON代码块
            json_pattern = r'```json\s*(.*?)\s*```'
            json_match = re.search(json_pattern, content, re.DOTALL)
            
            if json_match:
                json_str = json_match.group(1)
            else:
                # 尝试直接解析整个内容
                json_str = content.strip()
            
            # 解析JSON
            parsed_result = json.loads(json_str)
            
            # 验证必要字段
            required_fields = ["ui_elements", "test_scenarios", "analysis_summary"]
            for field in required_fields:
                if field not in parsed_result:
                    parsed_result[field] = [] if field != "analysis_summary" else "分析结果不完整"
            
            return parsed_result
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return {
                "ui_elements": [],
                "test_scenarios": [],
                "test_steps": [],
                "analysis_summary": f"JSON解析失败: {str(e)}",
                "confidence_score": 0.0,
                "raw_content": content
            }
        except Exception as e:
            logger.error(f"解析智能体输出失败: {str(e)}")
            return {
                "ui_elements": [],
                "test_scenarios": [],
                "test_steps": [],
                "analysis_summary": f"解析失败: {str(e)}",
                "confidence_score": 0.0,
                "raw_content": content
            }

    async def _build_analysis_result(self, analysis_data: Dict[str, Any], 
                                   request: AndroidAnalysisRequest) -> Dict[str, Any]:
        """构建最终的分析结果"""
        try:
            from app.core.messages.android import AndroidAnalysisResult, AndroidUIElement, AndroidTestStep
            
            # 构建UI元素列表
            ui_elements = []
            for element_data in analysis_data.get("ui_elements", []):
                ui_element = AndroidUIElement(**element_data)
                ui_elements.append(ui_element)
            
            # 构建测试步骤列表
            test_steps = []
            for step_data in analysis_data.get("test_steps", []):
                test_step = AndroidTestStep(**step_data)
                test_steps.append(test_step)
            
            # 构建分析结果
            analysis_result = AndroidAnalysisResult(
                ui_elements=ui_elements,
                test_scenarios=analysis_data.get("test_scenarios", []),
                test_steps=test_steps,
                analysis_summary=analysis_data.get("analysis_summary", ""),
                confidence_score=analysis_data.get("confidence_score", 0.0),
                activity_name=analysis_data.get("activity_name"),
                package_name=analysis_data.get("package_name"),
                screen_resolution=request.device_info.get("screen_resolution") if request.device_info else None
            )
            
            return analysis_result.model_dump()
            
        except Exception as e:
            logger.error(f"构建分析结果失败: {str(e)}")
            # 返回基础结果
            return {
                "ui_elements": [],
                "test_scenarios": [],
                "test_steps": [],
                "analysis_summary": f"构建结果失败: {str(e)}",
                "confidence_score": 0.0,
                "activity_name": None,
                "package_name": None,
                "raw_analysis": analysis_data
            }

    async def analyze_android_interface(self, screenshot_data: str, ui_hierarchy: Optional[str] = None,
                                      test_description: str = "Android界面分析",
                                      additional_context: Optional[str] = None,
                                      session_id: Optional[str] = None) -> Dict[str, Any]:
        """直接分析Android界面的公共方法"""
        try:
            if not session_id:
                session_id = str(uuid.uuid4())

            logger.info(f"开始Android界面分析: {session_id}")

            # 创建请求消息
            request = AndroidAnalysisRequest(
                session_id=session_id,
                screenshot_data=screenshot_data,
                ui_hierarchy=ui_hierarchy,
                test_description=test_description,
                additional_context=additional_context,
                generate_formats=["appium"]
            )

            # 创建分析智能体
            agent = self.create_android_expert_agent()

            # 准备多模态消息
            multimodal_message = await self._prepare_multimodal_message(request)

            # 运行智能体分析
            analysis_results = await self._run_agent_analysis(agent, multimodal_message)

            # 构建分析结果
            analysis_result = await self._build_analysis_result(analysis_results, request)

            return analysis_result

        except Exception as e:
            logger.error(f"Android界面分析失败: {session_id}, 错误: {str(e)}")
            raise
