"""
Android脚本执行智能体
负责执行Android自动化测试脚本，管理设备连接和测试执行
支持Appium、uiautomator2、pytest等多种执行环境
"""
import json
import uuid
import asyncio
import subprocess
import tempfile
import os
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from pathlib import Path

from autogen_core import message_handler, type_subscription, MessageContext
from loguru import logger

from app.core.messages.android import AndroidScriptExecutionMessage
from app.core.agents.base import BaseAgent
from app.core.types import TopicTypes, AgentTypes, AGENT_NAMES, AgentPlatform


@type_subscription(topic_type=TopicTypes.ANDROID_EXECUTOR.value)
class AndroidExecutorAgent(BaseAgent):
    """Android脚本执行智能体，负责执行各种类型的Android测试脚本"""

    def __init__(self, model_client_instance=None, **kwargs):
        """初始化Android脚本执行智能体"""
        super().__init__(
            agent_id=AgentTypes.ANDROID_EXECUTOR.value,
            agent_name=AGENT_NAMES[AgentTypes.ANDROID_EXECUTOR.value],
            platform=AgentPlatform.ANDROID,
            model_client_instance=model_client_instance,
            **kwargs
        )
        self.execution_records: Dict[str, Dict[str, Any]] = {}
        self.supported_frameworks = ["appium", "uiautomator2", "pytest", "unittest"]

        logger.info(f"Android脚本执行智能体初始化完成: {self.agent_name}")

    @message_handler
    async def handle_execution_request(self, message: AndroidScriptExecutionMessage, ctx: MessageContext) -> None:
        """处理Android脚本执行请求"""
        try:
            monitor_id = self.start_performance_monitoring()
            execution_id = str(uuid.uuid4())
            
            await self.send_response(f"🚀 开始执行Android测试: {execution_id}")
            
            # 创建执行记录
            self.execution_records[execution_id] = {
                "execution_id": execution_id,
                "status": "running",
                "start_time": datetime.now().isoformat(),
                "script_type": message.script_type,
                "script_content": message.script_content,
                "device_config": message.device_config,
                "execution_config": message.execution_config,
                "logs": [],
                "results": None,
                "error_message": None
            }
            
            # 执行Android测试脚本
            execution_result = await self._execute_android_script(
                execution_id,
                message.script_type,
                message.script_content,
                message.device_config,
                message.execution_config
            )
            
            # 更新执行记录
            self.execution_records[execution_id].update(execution_result)
            
            await self.send_response(
                f"✅ Android测试执行完成: {execution_result['status']}",
                is_final=True,
                result={
                    "execution_id": execution_id,
                    "execution_result": execution_result,
                    "metrics": self.end_performance_monitoring(monitor_id)
                }
            )

        except Exception as e:
            await self.handle_exception("handle_execution_request", e)

    async def _execute_android_script(self, execution_id: str, script_type: str, 
                                    script_content: str, device_config: Dict[str, Any],
                                    execution_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行Android测试脚本"""
        try:
            record = self.execution_records[execution_id]
            record["logs"].append(f"开始执行{script_type}测试脚本...")
            
            await self.send_response(f"📱 检查设备连接...")
            
            # 检查设备连接
            device_check = await self._check_device_connection(device_config)
            if not device_check["connected"]:
                raise Exception(f"设备连接失败: {device_check['error']}")
            
            await self.send_response(f"📄 准备测试脚本...")
            
            # 根据脚本类型选择执行方式
            if script_type.lower() in ["appium", "appium_python"]:
                execution_result = await self._execute_appium_script(
                    execution_id, script_content, device_config, execution_config
                )
            elif script_type.lower() in ["uiautomator2", "u2"]:
                execution_result = await self._execute_uiautomator2_script(
                    execution_id, script_content, device_config, execution_config
                )
            elif script_type.lower() == "pytest":
                execution_result = await self._execute_pytest_script(
                    execution_id, script_content, device_config, execution_config
                )
            else:
                raise ValueError(f"不支持的脚本类型: {script_type}")
            
            return execution_result
            
        except Exception as e:
            logger.error(f"执行Android脚本失败: {str(e)}")
            return {
                "status": "error",
                "end_time": datetime.now().isoformat(),
                "error_message": str(e),
                "duration": 0.0
            }

    async def _check_device_connection(self, device_config: Dict[str, Any]) -> Dict[str, Any]:
        """检查Android设备连接状态"""
        try:
            device_id = device_config.get("device_id", "")
            
            # 使用adb检查设备连接
            if device_id:
                cmd = ["adb", "-s", device_id, "get-state"]
            else:
                cmd = ["adb", "get-state"]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and b"device" in stdout:
                return {
                    "connected": True,
                    "device_state": stdout.decode().strip(),
                    "device_id": device_id or "default"
                }
            else:
                return {
                    "connected": False,
                    "error": stderr.decode() if stderr else "设备未连接"
                }
                
        except Exception as e:
            return {
                "connected": False,
                "error": f"检查设备连接失败: {str(e)}"
            }

    async def _execute_appium_script(self, execution_id: str, script_content: str,
                                   device_config: Dict[str, Any], execution_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行Appium测试脚本"""
        try:
            await self.send_response("🔧 配置Appium执行环境...")
            
            # 创建临时脚本文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(script_content)
                script_path = f.name
            
            try:
                # 设置环境变量
                env = os.environ.copy()
                env.update({
                    "DEVICE_ID": device_config.get("device_id", ""),
                    "PLATFORM_VERSION": device_config.get("platform_version", "11.0"),
                    "APP_PACKAGE": device_config.get("app_package", ""),
                    "APP_ACTIVITY": device_config.get("app_activity", ""),
                    "APPIUM_SERVER": execution_config.get("appium_server", "http://localhost:4723/wd/hub")
                })
                
                await self.send_response("▶️ 执行Appium测试...")
                
                # 执行脚本
                start_time = datetime.now()
                
                process = await asyncio.create_subprocess_exec(
                    "python", script_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    env=env
                )
                
                stdout, stderr = await process.communicate()
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                # 解析执行结果
                success = process.returncode == 0
                
                return {
                    "status": "passed" if success else "failed",
                    "end_time": end_time.isoformat(),
                    "duration": duration,
                    "stdout": stdout.decode() if stdout else "",
                    "stderr": stderr.decode() if stderr else "",
                    "return_code": process.returncode,
                    "framework": "appium"
                }
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(script_path)
                except:
                    pass
                    
        except Exception as e:
            logger.error(f"执行Appium脚本失败: {str(e)}")
            return {
                "status": "error",
                "end_time": datetime.now().isoformat(),
                "error_message": str(e),
                "duration": 0.0,
                "framework": "appium"
            }

    async def _execute_uiautomator2_script(self, execution_id: str, script_content: str,
                                         device_config: Dict[str, Any], execution_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行uiautomator2测试脚本"""
        try:
            await self.send_response("🔧 配置uiautomator2执行环境...")
            
            # 创建临时脚本文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(script_content)
                script_path = f.name
            
            try:
                # 设置环境变量
                env = os.environ.copy()
                env.update({
                    "DEVICE_ID": device_config.get("device_id", ""),
                    "APP_PACKAGE": device_config.get("app_package", "")
                })
                
                await self.send_response("▶️ 执行uiautomator2测试...")
                
                # 执行脚本
                start_time = datetime.now()
                
                process = await asyncio.create_subprocess_exec(
                    "python", script_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    env=env
                )
                
                stdout, stderr = await process.communicate()
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                # 解析执行结果
                success = process.returncode == 0
                
                return {
                    "status": "passed" if success else "failed",
                    "end_time": end_time.isoformat(),
                    "duration": duration,
                    "stdout": stdout.decode() if stdout else "",
                    "stderr": stderr.decode() if stderr else "",
                    "return_code": process.returncode,
                    "framework": "uiautomator2"
                }
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(script_path)
                except:
                    pass
                    
        except Exception as e:
            logger.error(f"执行uiautomator2脚本失败: {str(e)}")
            return {
                "status": "error",
                "end_time": datetime.now().isoformat(),
                "error_message": str(e),
                "duration": 0.0,
                "framework": "uiautomator2"
            }

    async def _execute_pytest_script(self, execution_id: str, script_content: str,
                                    device_config: Dict[str, Any], execution_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行pytest测试脚本"""
        try:
            await self.send_response("🔧 配置pytest执行环境...")
            
            # 创建临时测试目录
            with tempfile.TemporaryDirectory() as temp_dir:
                script_path = os.path.join(temp_dir, "test_android.py")
                
                # 写入脚本内容
                with open(script_path, 'w', encoding='utf-8') as f:
                    f.write(script_content)
                
                # 创建pytest配置文件
                pytest_ini = os.path.join(temp_dir, "pytest.ini")
                with open(pytest_ini, 'w') as f:
                    f.write("""[tool:pytest]
testpaths = .
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short --strict-markers
""")
                
                # 设置环境变量
                env = os.environ.copy()
                env.update({
                    "DEVICE_ID": device_config.get("device_id", ""),
                    "PLATFORM_VERSION": device_config.get("platform_version", "11.0"),
                    "APP_PACKAGE": device_config.get("app_package", ""),
                    "APP_ACTIVITY": device_config.get("app_activity", ""),
                    "APPIUM_SERVER": execution_config.get("appium_server", "http://localhost:4723/wd/hub")
                })
                
                await self.send_response("▶️ 执行pytest测试...")
                
                # 执行pytest
                start_time = datetime.now()
                
                process = await asyncio.create_subprocess_exec(
                    "pytest", script_path, "-v", "--tb=short",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    env=env,
                    cwd=temp_dir
                )
                
                stdout, stderr = await process.communicate()
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                # 解析pytest结果
                stdout_text = stdout.decode() if stdout else ""
                stderr_text = stderr.decode() if stderr else ""
                
                # 简单的结果解析
                if "FAILED" in stdout_text:
                    status = "failed"
                elif "ERROR" in stdout_text:
                    status = "error"
                elif "passed" in stdout_text:
                    status = "passed"
                else:
                    status = "unknown"
                
                return {
                    "status": status,
                    "end_time": end_time.isoformat(),
                    "duration": duration,
                    "stdout": stdout_text,
                    "stderr": stderr_text,
                    "return_code": process.returncode,
                    "framework": "pytest"
                }
                
        except Exception as e:
            logger.error(f"执行pytest脚本失败: {str(e)}")
            return {
                "status": "error",
                "end_time": datetime.now().isoformat(),
                "error_message": str(e),
                "duration": 0.0,
                "framework": "pytest"
            }

    async def get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """获取执行状态"""
        return self.execution_records.get(execution_id)

    async def list_executions(self) -> List[Dict[str, Any]]:
        """列出所有执行记录"""
        return list(self.execution_records.values())

    async def cancel_execution(self, execution_id: str) -> bool:
        """取消执行（如果可能）"""
        try:
            if execution_id in self.execution_records:
                record = self.execution_records[execution_id]
                if record["status"] == "running":
                    record["status"] = "cancelled"
                    record["end_time"] = datetime.now().isoformat()
                    return True
            return False
        except Exception as e:
            logger.error(f"取消执行失败: {str(e)}")
            return False
