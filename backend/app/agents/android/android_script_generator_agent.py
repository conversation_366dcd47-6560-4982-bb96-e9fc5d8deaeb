"""
Android脚本生成智能体
负责根据Android界面分析结果生成自动化测试脚本
支持Appium、uiautomator2、pytest等多种框架
"""
import json
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from autogen_agentchat.agents import AssistantAgent
from autogen_core import message_handler, type_subscription, MessageContext
from loguru import logger

from app.core.messages.android import AndroidScriptGenerationMessage, AndroidGeneratedScript
from app.core.agents.base import BaseAgent
from app.core.types import TopicTypes, AgentTypes, AGENT_NAMES, MessageRegion, AgentPlatform


@type_subscription(topic_type=TopicTypes.ANDROID_SCRIPT_GENERATOR.value)
class AndroidScriptGeneratorAgent(BaseAgent):
    """Android脚本生成智能体，负责生成各种Android自动化测试脚本"""

    def __init__(self, model_client_instance=None, **kwargs):
        """初始化Android脚本生成智能体"""
        super().__init__(
            agent_id=AgentTypes.ANDROID_SCRIPT_GENERATOR.value,
            agent_name=AGENT_NAMES[AgentTypes.ANDROID_SCRIPT_GENERATOR.value],
            platform=AgentPlatform.ANDROID,
            model_client_instance=model_client_instance,
            **kwargs
        )
        self._prompt_template = self._build_prompt_template()
        self.metrics = None

        logger.info(f"Android脚本生成智能体初始化完成: {self.agent_name}")

    @classmethod
    def create_assistant_agent(cls, model_client_instance=None, **kwargs) -> AssistantAgent:
        """创建用于Android脚本生成的AssistantAgent实例"""
        from app.agents.factory import agent_factory

        return agent_factory.create_assistant_agent(
            name="android_script_generator",
            system_message=cls._build_prompt_template(),
            model_client_type="deepseek",
            model_client_stream=True,
            **kwargs
        )

    @staticmethod
    def _build_prompt_template() -> str:
        """构建Android脚本生成提示模板"""
        return """你是Android自动化测试脚本生成专家，精通多种Android自动化测试框架。

## 🎯 核心任务
根据Android界面分析结果，生成高质量的自动化测试脚本，支持多种框架和测试场景。

## 🛠️ 支持的测试框架

### 1. Appium + Python (推荐)
```python
from appium import webdriver
from appium.webdriver.common.appiumby import AppiumBy
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pytest
import time

class TestAndroidApp:
    def setup_method(self):
        desired_caps = {
            'platformName': 'Android',
            'platformVersion': '11.0',
            'deviceName': 'Android Emulator',
            'appPackage': 'com.example.app',
            'appActivity': '.MainActivity',
            'automationName': 'UiAutomator2',
            'noReset': True
        }
        self.driver = webdriver.Remote('http://localhost:4723/wd/hub', desired_caps)
        self.wait = WebDriverWait(self.driver, 10)
    
    def test_login_flow(self):
        # 等待用户名输入框出现
        username_field = self.wait.until(
            EC.presence_of_element_located((AppiumBy.ID, "com.example.app:id/et_username"))
        )
        username_field.send_keys("testuser")
        
        # 输入密码
        password_field = self.driver.find_element(AppiumBy.ID, "com.example.app:id/et_password")
        password_field.send_keys("testpass")
        
        # 点击登录按钮
        login_button = self.driver.find_element(AppiumBy.ID, "com.example.app:id/btn_login")
        login_button.click()
        
        # 验证登录成功
        success_message = self.wait.until(
            EC.presence_of_element_located((AppiumBy.ID, "com.example.app:id/tv_welcome"))
        )
        assert "欢迎" in success_message.text
    
    def teardown_method(self):
        self.driver.quit()
```

### 2. uiautomator2 + Python
```python
import uiautomator2 as u2
import pytest
import time

class TestAndroidApp:
    def setup_method(self):
        self.device = u2.connect()  # 连接设备
        self.device.app_start("com.example.app")
        time.sleep(2)
    
    def test_login_flow(self):
        # 输入用户名
        self.device(resourceId="com.example.app:id/et_username").set_text("testuser")
        
        # 输入密码
        self.device(resourceId="com.example.app:id/et_password").set_text("testpass")
        
        # 点击登录按钮
        self.device(resourceId="com.example.app:id/btn_login").click()
        
        # 验证登录成功
        assert self.device(resourceId="com.example.app:id/tv_welcome").exists(timeout=10)
        welcome_text = self.device(resourceId="com.example.app:id/tv_welcome").get_text()
        assert "欢迎" in welcome_text
    
    def teardown_method(self):
        self.device.app_stop("com.example.app")
```

### 3. Appium + Java (TestNG)
```java
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.options.UiAutomator2Options;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.testng.Assert;
import org.testng.annotations.*;
import java.net.URL;
import java.time.Duration;

public class AndroidAppTest {
    private AndroidDriver driver;
    private WebDriverWait wait;
    
    @BeforeMethod
    public void setUp() throws Exception {
        UiAutomator2Options options = new UiAutomator2Options();
        options.setPlatformName("Android");
        options.setPlatformVersion("11.0");
        options.setDeviceName("Android Emulator");
        options.setAppPackage("com.example.app");
        options.setAppActivity(".MainActivity");
        options.setNoReset(true);
        
        driver = new AndroidDriver(new URL("http://localhost:4723/wd/hub"), options);
        wait = new WebDriverWait(driver, Duration.ofSeconds(10));
    }
    
    @Test
    public void testLoginFlow() {
        // 输入用户名
        WebElement usernameField = wait.until(
            ExpectedConditions.presenceOfElementLocated(By.id("com.example.app:id/et_username"))
        );
        usernameField.sendKeys("testuser");
        
        // 输入密码
        WebElement passwordField = driver.findElement(By.id("com.example.app:id/et_password"));
        passwordField.sendKeys("testpass");
        
        // 点击登录按钮
        WebElement loginButton = driver.findElement(By.id("com.example.app:id/btn_login"));
        loginButton.click();
        
        // 验证登录成功
        WebElement welcomeMessage = wait.until(
            ExpectedConditions.presenceOfElementLocated(By.id("com.example.app:id/tv_welcome"))
        );
        Assert.assertTrue(welcomeMessage.getText().contains("欢迎"));
    }
    
    @AfterMethod
    public void tearDown() {
        if (driver != null) {
            driver.quit();
        }
    }
}
```

## 📱 Android特有操作支持

### 1. 手势操作
```python
# 滑动操作
def swipe_up(self):
    size = self.driver.get_window_size()
    start_x = size['width'] // 2
    start_y = size['height'] * 0.8
    end_y = size['height'] * 0.2
    self.driver.swipe(start_x, start_y, start_x, end_y, 1000)

# 长按操作
def long_press_element(self, element):
    from appium.webdriver.common.touch_action import TouchAction
    action = TouchAction(self.driver)
    action.long_press(element).release().perform()

# 双击操作
def double_tap_element(self, element):
    from appium.webdriver.common.multi_action import MultiAction
    from appium.webdriver.common.touch_action import TouchAction
    action = TouchAction(self.driver)
    action.tap(element).tap(element)
    multi_action = MultiAction(self.driver)
    multi_action.add(action)
    multi_action.perform()
```

### 2. 系统交互
```python
# 处理系统权限弹窗
def handle_permission_dialog(self):
    try:
        allow_button = self.driver.find_element(AppiumBy.ID, "com.android.permissioncontroller:id/permission_allow_button")
        allow_button.click()
    except:
        pass

# 处理键盘
def hide_keyboard(self):
    if self.driver.is_keyboard_shown():
        self.driver.hide_keyboard()

# 返回键
def press_back(self):
    self.driver.back()

# Home键
def press_home(self):
    self.driver.press_keycode(3)
```

### 3. 应用状态管理
```python
# 启动应用
def start_app(self, package_name):
    self.driver.activate_app(package_name)

# 关闭应用
def close_app(self, package_name):
    self.driver.terminate_app(package_name)

# 重启应用
def restart_app(self, package_name):
    self.driver.terminate_app(package_name)
    time.sleep(1)
    self.driver.activate_app(package_name)
```

## 🎯 元素定位策略

### 1. 定位方式优先级
1. **resource-id**: 最稳定的定位方式
2. **accessibility-id**: 无障碍标识符
3. **text**: 文本内容（适用于静态文本）
4. **class**: 控件类型（需要结合其他属性）
5. **xpath**: 复杂定位（性能较差，谨慎使用）
6. **坐标**: 最后选择（设备相关性强）

### 2. 智能等待策略
```python
# 显式等待
def wait_for_element(self, locator, timeout=10):
    return WebDriverWait(self.driver, timeout).until(
        EC.presence_of_element_located(locator)
    )

# 等待元素可点击
def wait_for_clickable(self, locator, timeout=10):
    return WebDriverWait(self.driver, timeout).until(
        EC.element_to_be_clickable(locator)
    )

# 等待文本出现
def wait_for_text(self, locator, text, timeout=10):
    return WebDriverWait(self.driver, timeout).until(
        EC.text_to_be_present_in_element(locator, text)
    )
```

## 📋 脚本生成要求

### 1. 代码结构
- 使用Page Object模式组织代码
- 包含完整的setup和teardown
- 添加详细的注释说明
- 包含异常处理机制

### 2. 测试数据
- 支持参数化测试
- 包含边界值测试数据
- 考虑数据驱动测试

### 3. 断言验证
- 多层次验证（元素存在、文本内容、状态等）
- 包含截图功能用于调试
- 详细的错误信息

### 4. 最佳实践
- 遵循PEP 8代码规范（Python）
- 使用有意义的变量和方法名
- 避免硬编码，使用配置文件
- 包含性能优化建议

请根据提供的Android界面分析结果，生成完整、可执行的测试脚本。"""

    @message_handler
    async def handle_message(self, message: AndroidScriptGenerationMessage, ctx: MessageContext) -> None:
        """处理Android脚本生成请求"""
        try:
            monitor_id = self.start_performance_monitoring()

            await self.send_response("🚀 开始生成Android自动化脚本...")

            # 创建脚本生成智能体
            agent = self.create_assistant_agent(model_client_instance=self.model_client)

            # 生成脚本
            generated_scripts = await self._generate_scripts(agent, message)

            self.metrics = self.end_performance_monitoring(monitor_id)

            await self.send_response(
                "✅ Android脚本生成完成",
                is_final=True,
                result={
                    "generated_scripts": generated_scripts,
                    "script_count": len(generated_scripts),
                    "metrics": self.metrics
                }
            )

        except Exception as e:
            await self.handle_exception("handle_message", e)

    async def _generate_scripts(self, agent: AssistantAgent, message: AndroidScriptGenerationMessage) -> List[Dict[str, Any]]:
        """生成Android测试脚本"""
        try:
            generated_scripts = []

            # 为每种请求的格式生成脚本
            for script_format in message.generate_formats:
                await self.send_response(f"📝 生成{script_format}格式脚本...")

                script_content = await self._generate_single_script(agent, message, script_format)
                
                if script_content:
                    script = AndroidGeneratedScript(
                        format=script_format,
                        content=script_content,
                        estimated_duration="5-10分钟",
                        script_type="automation",
                        framework=script_format
                    )
                    generated_scripts.append(script.model_dump())

            return generated_scripts

        except Exception as e:
            logger.error(f"生成Android脚本失败: {str(e)}")
            return []

    async def _generate_single_script(self, agent: AssistantAgent, message: AndroidScriptGenerationMessage, 
                                    script_format: str) -> Optional[str]:
        """生成单个格式的脚本"""
        try:
            # 构建生成提示
            generation_prompt = self._build_generation_prompt(message, script_format)

            # 运行智能体生成
            from autogen_agentchat.teams import RoundRobinGroupChat
            from autogen_core.memory import ListMemory
            from autogen_agentchat.messages import TextMessage

            team = RoundRobinGroupChat([agent], memory=ListMemory())
            result = await team.run(
                task=TextMessage(content=generation_prompt, source="user"),
                termination_condition=lambda messages: len(messages) >= 2
            )

            if result.messages:
                return result.messages[-1].content

            return None

        except Exception as e:
            logger.error(f"生成{script_format}脚本失败: {str(e)}")
            return None

    def _build_generation_prompt(self, message: AndroidScriptGenerationMessage, script_format: str) -> str:
        """构建脚本生成提示"""
        analysis_result = message.analysis_result

        prompt = f"""
请基于以下Android界面分析结果，生成{script_format}格式的自动化测试脚本。

## 界面分析结果
{json.dumps(analysis_result, ensure_ascii=False, indent=2)}

## 测试需求
{message.test_description}

## 脚本格式要求
- 格式: {script_format}
- 包含完整的测试类和方法
- 添加详细注释
- 包含setup和teardown方法
- 使用最佳实践和设计模式

## 特殊要求
{message.additional_context or "无特殊要求"}

请生成完整可执行的{script_format}测试脚本，包含所有必要的导入语句和配置。
"""
        return prompt.strip()

    async def generate_android_script(self, analysis_result: Dict[str, Any], 
                                    test_description: str = "Android自动化测试",
                                    script_formats: List[str] = None,
                                    additional_context: Optional[str] = None,
                                    session_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """直接生成Android脚本的公共方法"""
        try:
            if not session_id:
                session_id = str(uuid.uuid4())

            if not script_formats:
                script_formats = ["appium"]

            logger.info(f"开始生成Android脚本: {session_id}")

            # 创建生成消息
            message = AndroidScriptGenerationMessage(
                session_id=session_id,
                requirement_id=session_id,
                analysis_result=analysis_result,
                test_description=test_description,
                generate_formats=script_formats,
                additional_context=additional_context
            )

            # 创建脚本生成智能体
            agent = self.create_assistant_agent(model_client_instance=self.model_client)

            # 生成脚本
            generated_scripts = await self._generate_scripts(agent, message)

            return generated_scripts

        except Exception as e:
            logger.error(f"Android脚本生成失败: {session_id}, 错误: {str(e)}")
            raise
